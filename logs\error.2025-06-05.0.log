2025-06-05 21:16:45.351 ERROR --- [pool-1-thread-1] c.s.service.XuexitongApiService : 从页面HTML中未找到token
2025-06-05 21:16:45.353 ERROR --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取页面token失败，无法继续预约
2025-06-05 21:16:45.468 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor14.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 21:51:42.066 ERROR --- [pool-1-thread-1] c.s.service.XuexitongApiService : 从页面HTML中未找到token
2025-06-05 21:51:42.256 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor16.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 22:12:45.295 ERROR --- [           main] o.s.b.d.LoggingFailureAnalysisReporter : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-06-05 22:14:10.873 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 22:21:21.270 ERROR --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 22:23:16.265 ERROR --- [pool-1-thread-3] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
