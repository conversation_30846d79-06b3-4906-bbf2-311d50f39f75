2025-06-05 15:37:43.477 ERROR %PARSER_ERROR[pid] --- [           main] o.s.boot.SpringApplication               : Application run failed
java.lang.IllegalStateException: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.pattern.parser.Compiler@e70f13a - There is no conversion class registered for conversion word [pid]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@e70f13a - [pid] is not a valid conversion word
ERROR in ch.qos.logback.core.pattern.parser.Compiler@3d3e5463 - There is no conversion class registered for conversion word [pid]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@3d3e5463 - [pid] is not a valid conversion word
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:344)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:298)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:246)
	at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:223)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:176)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:169)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:143)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:131)
	at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:85)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:66)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114)
	at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:65)
	at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:301)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.seatmaster.SeatReservationApplication.main(SeatReservationApplication.java:13)
Caused by: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.pattern.parser.Compiler@e70f13a - There is no conversion class registered for conversion word [pid]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@e70f13a - [pid] is not a valid conversion word
ERROR in ch.qos.logback.core.pattern.parser.Compiler@3d3e5463 - There is no conversion class registered for conversion word [pid]
ERROR in ch.qos.logback.core.pattern.parser.Compiler@3d3e5463 - [pid] is not a valid conversion word
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:179)
	at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:80)
	at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:60)
	at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:132)
	at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:329)
	... 18 common frames omitted
2025-06-05 15:39:01.753  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 15:39:01.764  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 132972 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 15:39:01.765 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 15:39:01.765  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 15:39:03.061  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 15:39:03.063  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 15:39:03.063  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 15:39:03.063  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 15:39:03.069  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 15:39:03.077  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 15:39:03.082  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 15:39:03.082  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 15:39:03.214  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 15:39:03.216  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1403 ms
2025-06-05 15:39:03.272 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 15:39:03.996  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 34a994c4-3413-4efb-9f1b-ea6c6dba73fb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 15:39:04.144  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@41aebbb4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@afde064, org.springframework.security.web.context.SecurityContextPersistenceFilter@526f6427, org.springframework.security.web.header.HeaderWriterFilter@2322e56f, org.springframework.web.filter.CorsFilter@5a592c70, org.springframework.security.web.authentication.logout.LogoutFilter@512abf25, com.seatmaster.filter.JwtAuthenticationFilter@4b76aa5a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@492521c4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@15605d83, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@252ec02e, org.springframework.security.web.session.SessionManagementFilter@5968800d, org.springframework.security.web.access.ExceptionTranslationFilter@1e226bcd, org.springframework.security.web.access.intercept.AuthorizationFilter@2e6d76ba]
2025-06-05 15:39:04.445  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 15:39:04.479  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 15:39:04.493  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 3.437 seconds (JVM running for 3.887)
2025-06-05 15:40:00.111  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 15:40:00.286  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 15:44:25.571  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-05 15:44:25.582  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-05 15:45:20.862  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 15:45:20.875  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 210388 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 15:45:20.876 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 15:45:20.877  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 15:45:22.449  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 15:45:22.450  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 15:45:22.451  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 15:45:22.451  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 15:45:22.468  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 15:45:22.479  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 15:45:22.483  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 15:45:22.484  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 15:45:22.635  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 15:45:22.636  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1701 ms
2025-06-05 15:45:22.712 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 15:45:23.579  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f98efa7b-064d-4a8a-a571-940e05429e8e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 15:45:23.693  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@cdb3c85, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@37606fee, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ef18604, org.springframework.security.web.header.HeaderWriterFilter@3fde2209, org.springframework.web.filter.CorsFilter@33d28f0a, org.springframework.security.web.authentication.logout.LogoutFilter@463afa6e, com.seatmaster.filter.JwtAuthenticationFilter@6dab01d9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@16cf8438, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5968800d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48d739f, org.springframework.security.web.session.SessionManagementFilter@7c70aae1, org.springframework.security.web.access.ExceptionTranslationFilter@4f114b, org.springframework.security.web.access.intercept.AuthorizationFilter@512abf25]
2025-06-05 15:45:24.023  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 15:45:24.063  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 15:45:24.078  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 4.0 seconds (JVM running for 4.518)
2025-06-05 15:46:00.079  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 15:46:00.239  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 15:46:18.585  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-05 15:46:18.593  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-05 15:46:34.778  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 15:46:34.795  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 10000 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 15:46:34.796 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 15:46:34.798  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 15:46:36.281  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 15:46:36.283  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 15:46:36.283  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 15:46:36.284  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 15:46:36.291  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 15:46:36.302  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 15:46:36.305  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 15:46:36.306  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 15:46:36.441  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 15:46:36.443  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1568 ms
2025-06-05 15:46:36.507 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 15:46:37.215  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: f9b066cb-0576-4c17-baa7-5820cc6d530c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 15:46:37.318  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@578a5ce8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c453c34, org.springframework.security.web.context.SecurityContextPersistenceFilter@5b02a984, org.springframework.security.web.header.HeaderWriterFilter@585513a8, org.springframework.web.filter.CorsFilter@16cb9989, org.springframework.security.web.authentication.logout.LogoutFilter@443a06ad, com.seatmaster.filter.JwtAuthenticationFilter@7c112f5f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@391d1e33, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6d69a0d3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3dfd6220, org.springframework.security.web.session.SessionManagementFilter@7aded903, org.springframework.security.web.access.ExceptionTranslationFilter@59c43561, org.springframework.security.web.access.intercept.AuthorizationFilter@6da54910]
2025-06-05 15:46:37.624  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 15:46:37.661  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 15:46:37.674  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 3.529 seconds (JVM running for 4.001)
2025-06-05 15:47:00.085  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 15:47:00.240  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 15:47:23.161  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-05 15:47:23.197  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-05 16:03:17.072  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 16:03:17.098  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 113068 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 16:03:17.100 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 16:03:17.101  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 16:03:18.681  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 16:03:18.682  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 16:03:18.682  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 16:03:18.682  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 16:03:18.703  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 16:03:18.713  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 16:03:18.717  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 16:03:18.717  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 16:03:18.870  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 16:03:18.871  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1686 ms
2025-06-05 16:03:18.943 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 16:03:20.069  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 249585e9-c086-4cf6-a26c-ead0c2d64c76

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 16:03:20.221  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@8641b7d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@37c7766e, org.springframework.security.web.context.SecurityContextPersistenceFilter@2a0b901c, org.springframework.security.web.header.HeaderWriterFilter@3e5beab5, org.springframework.web.filter.CorsFilter@3fb450d7, org.springframework.security.web.authentication.logout.LogoutFilter@2e6d76ba, com.seatmaster.filter.JwtAuthenticationFilter@7c112f5f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6544899b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65da01f4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b7ed03e, org.springframework.security.web.session.SessionManagementFilter@15605d83, org.springframework.security.web.access.ExceptionTranslationFilter@372f0a99, org.springframework.security.web.access.intercept.AuthorizationFilter@1cd2ff5b]
2025-06-05 16:03:20.689  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 16:03:20.733  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 16:03:20.748  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 4.529 seconds (JVM running for 5.122)
2025-06-05 16:04:00.117  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 16:04:00.333  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 20:03:17.076  WARN --- [l-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3h52m46s501ms785µs700ns).
2025-06-05 20:06:53.019  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 20:06:53.030  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 128848 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 20:06:53.030 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 20:06:53.033  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 20:06:54.686  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 20:06:54.688  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 20:06:54.688  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 20:06:54.688  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 20:06:54.712  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 20:06:54.723  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 20:06:54.727  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 20:06:54.728  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 20:06:54.896  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 20:06:54.896  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1813 ms
2025-06-05 20:06:54.963 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 20:06:55.809  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 5062aebe-3825-4343-b827-67ad418c26fb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 20:06:55.924  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3dfd6220, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3815146b, org.springframework.security.web.context.SecurityContextPersistenceFilter@66f16742, org.springframework.security.web.header.HeaderWriterFilter@1a1f79ce, org.springframework.web.filter.CorsFilter@6de43bc1, org.springframework.security.web.authentication.logout.LogoutFilter@15605d83, com.seatmaster.filter.JwtAuthenticationFilter@7c112f5f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3c54ddec, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3fde2209, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@27a9f025, org.springframework.security.web.session.SessionManagementFilter@5b02a984, org.springframework.security.web.access.ExceptionTranslationFilter@7b18658a, org.springframework.security.web.access.intercept.AuthorizationFilter@65da01f4]
2025-06-05 20:06:56.277  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 20:06:56.334  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 20:06:56.356  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 3.985 seconds (JVM running for 4.467)
2025-06-05 20:07:00.076  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 20:07:00.257  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 20:19:16.209  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 20:19:16.222  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 144700 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 20:19:16.223 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 20:19:16.224  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 20:19:17.707  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 20:19:17.709  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 20:19:17.709  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 20:19:17.710  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 20:19:17.723  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 20:19:17.736  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 20:19:17.740  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 20:19:17.741  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 20:19:17.908  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 20:19:17.910  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1634 ms
2025-06-05 20:19:17.984 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 20:19:18.910  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 822dc128-a4cd-4191-9020-33433d3198fc

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 20:19:19.038  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@d2708a7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@508ad266, org.springframework.security.web.context.SecurityContextPersistenceFilter@611d0763, org.springframework.security.web.header.HeaderWriterFilter@261f359f, org.springframework.web.filter.CorsFilter@2313db84, org.springframework.security.web.authentication.logout.LogoutFilter@7c8874ef, com.seatmaster.filter.JwtAuthenticationFilter@279dd959, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1e226bcd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@38732364, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@216372b7, org.springframework.security.web.session.SessionManagementFilter@67acfde9, org.springframework.security.web.access.ExceptionTranslationFilter@7aded903, org.springframework.security.web.access.intercept.AuthorizationFilter@51ac12ac]
2025-06-05 20:19:19.429  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 20:19:19.472  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 20:19:19.486  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 4.092 seconds (JVM running for 4.566)
2025-06-05 20:20:00.090  INFO --- [   scheduling-3] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 20:20:00.273  INFO --- [   scheduling-3] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 20:34:01.795  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 20:34:01.820  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 100284 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 20:34:01.823 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 20:34:01.824  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 20:34:03.380  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 20:34:03.382  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 20:34:03.382  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 20:34:03.382  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 20:34:03.390  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 20:34:03.400  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 20:34:03.403  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 20:34:03.403  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 20:34:03.557  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 20:34:03.558  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1673 ms
2025-06-05 20:34:03.626 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 20:34:04.431  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 681da2de-f4ab-4c37-96e4-7f5d4a042f29

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 20:34:04.551  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@61394494, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@578a5ce8, org.springframework.security.web.context.SecurityContextPersistenceFilter@261f359f, org.springframework.security.web.header.HeaderWriterFilter@634f58d2, org.springframework.web.filter.CorsFilter@7c453c34, org.springframework.security.web.authentication.logout.LogoutFilter@492521c4, com.seatmaster.filter.JwtAuthenticationFilter@1a8df0b3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@57186526, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3c54ddec, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@16cb9989, org.springframework.security.web.session.SessionManagementFilter@76216830, org.springframework.security.web.access.ExceptionTranslationFilter@19b75b2b, org.springframework.security.web.access.intercept.AuthorizationFilter@6544899b]
2025-06-05 20:34:04.900  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 20:34:04.943  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 20:34:04.957  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 4.011 seconds (JVM running for 4.586)
2025-06-05 20:35:00.098  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 20:35:00.279  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 20:50:20.632  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 20:50:20.641  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 14.0.2 on LAPTOP-S5NOOCEM with PID 162284 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 20:50:20.642 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 20:50:20.643  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 20:50:22.082  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 20:50:22.083  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 20:50:22.084  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 20:50:22.084  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 20:50:22.091  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 20:50:22.103  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 20:50:22.104  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 20:50:22.104  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 20:50:22.257  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 20:50:22.259  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1564 ms
2025-06-05 20:50:22.327 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 20:50:23.120  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 6033f7b0-e76d-44c4-8e9a-2e0ed62bc037

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 20:50:23.217  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@37c7766e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3fb450d7, org.springframework.security.web.context.SecurityContextPersistenceFilter@3d104c9b, org.springframework.security.web.header.HeaderWriterFilter@7c588adc, org.springframework.web.filter.CorsFilter@4b7ed03e, org.springframework.security.web.authentication.logout.LogoutFilter@22a6d75c, com.seatmaster.filter.JwtAuthenticationFilter@7c112f5f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6da54910, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22f02996, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@636985df, org.springframework.security.web.session.SessionManagementFilter@1fb2eec, org.springframework.security.web.access.ExceptionTranslationFilter@16cf8438, org.springframework.security.web.access.intercept.AuthorizationFilter@38ac8968]
2025-06-05 20:50:23.614  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 20:50:23.654  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 20:50:23.668  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 3.852 seconds (JVM running for 4.32)
2025-06-05 20:51:00.152  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 20:51:00.326  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 21:13:17.379  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 211672 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 21:13:17.379  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 21:13:17.385 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 21:13:17.385  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 21:13:19.921  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 21:13:19.925  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 21:13:19.925  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 21:13:19.925  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 21:13:19.952  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 21:13:19.960  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 21:13:19.967  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 21:13:19.968  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 21:13:20.123  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 21:13:20.123  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2624 ms
2025-06-05 21:13:20.231 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 21:13:21.700  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a4a46e20-e1e2-4adb-a755-ecda16fd4be2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 21:13:21.872  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@22df874e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@654c1a54, org.springframework.security.web.context.SecurityContextPersistenceFilter@6f95cd51, org.springframework.security.web.header.HeaderWriterFilter@44f9779c, org.springframework.web.filter.CorsFilter@5bdaf2ce, org.springframework.security.web.authentication.logout.LogoutFilter@e044b4a, com.seatmaster.filter.JwtAuthenticationFilter@15cafec7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@55caeb35, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c383e33, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@42d236fb, org.springframework.security.web.session.SessionManagementFilter@40147317, org.springframework.security.web.access.ExceptionTranslationFilter@6c000e0c, org.springframework.security.web.access.intercept.AuthorizationFilter@e36bb2a]
2025-06-05 21:13:22.452  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 21:13:22.523  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 21:13:22.555  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 6.412 seconds (JVM running for 7.287)
2025-06-05 21:14:00.110  INFO --- [   scheduling-4] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 21:14:00.335  INFO --- [   scheduling-4] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 21:14:29.929  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-05 21:14:30.024  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-05 21:25:02.682  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 21:25:02.702  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 146268 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 21:25:02.705 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 21:25:02.708  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 21:25:05.496  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 21:25:05.509  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 21:25:05.516  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 21:25:05.517  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 21:25:05.579  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 21:25:05.600  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 21:25:05.614  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 21:25:05.614  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 21:25:05.827  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 21:25:05.828  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2991 ms
2025-06-05 21:25:06.009 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 21:25:07.960  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: dd46bbbc-4c02-4817-be12-7e438d08723d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 21:25:08.220  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@367795c7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@d2387c8, org.springframework.security.web.context.SecurityContextPersistenceFilter@57f64f5e, org.springframework.security.web.header.HeaderWriterFilter@49298ce7, org.springframework.web.filter.CorsFilter@3956b302, org.springframework.security.web.authentication.logout.LogoutFilter@43d455c9, com.seatmaster.filter.JwtAuthenticationFilter@3e5499cc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@194152cf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c30b71f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1500e009, org.springframework.security.web.session.SessionManagementFilter@67fe380b, org.springframework.security.web.access.ExceptionTranslationFilter@240139e1, org.springframework.security.web.access.intercept.AuthorizationFilter@c7a977f]
2025-06-05 21:25:08.892  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 21:25:08.978  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 21:25:09.013  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 7.592 seconds (JVM running for 8.381)
2025-06-05 21:26:00.208  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 21:26:00.770  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 21:26:27.003  INFO --- [nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 21:26:27.004  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-05 21:26:27.007  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-05 21:26:27.438  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 21:26:27.444  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 21:26:27.446  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 21:26:27.463  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 21:26:27.464  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 21:26:27.467  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 21:26:27.478  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 21:26:27.478  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 21:26:27.479  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=AUTO_PENDING, 开始时间=15:30, 结束时间=20:30
2025-06-05 21:26:27.484  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
2025-06-05 21:48:27.863  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-05 21:48:27.927  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-05 22:22:24.780  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 22:22:24.789  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 127976 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster\backend)
2025-06-05 22:22:24.789 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 22:22:24.791  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 22:22:26.893  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8082 (http)
2025-06-05 22:22:26.893  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 22:22:26.893  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 22:22:26.893  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 22:22:26.901  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 22:22:26.912  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8082"]
2025-06-05 22:22:26.916  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 22:22:26.916  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 22:22:27.030  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 22:22:27.031  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2142 ms
2025-06-05 22:22:27.136 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 22:22:28.350  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: d75a4c4a-c1f1-42e0-ad0f-b79f91fe676d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 22:22:28.540  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@c827db, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@377c68c6, org.springframework.security.web.context.SecurityContextPersistenceFilter@43d455c9, org.springframework.security.web.header.HeaderWriterFilter@30cdae70, org.springframework.web.filter.CorsFilter@538cd0f2, org.springframework.security.web.authentication.logout.LogoutFilter@d4602a, com.seatmaster.filter.JwtAuthenticationFilter@183e8023, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9ec531, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@210f0cc1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@238ad8c, org.springframework.security.web.session.SessionManagementFilter@44f9779c, org.springframework.security.web.access.ExceptionTranslationFilter@bf71cec, org.springframework.security.web.access.intercept.AuthorizationFilter@11a82d0f]
2025-06-05 22:22:29.251  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8082"]
2025-06-05 22:22:29.299  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8082 (http) with context path ''
2025-06-05 22:22:29.318  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 5.45 seconds (JVM running for 6.137)
2025-06-05 22:23:00.104  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 22:23:00.422  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
