# 测试Java重构单机版自动预约系统
# 用户：18755869972

$baseUrl = "http://localhost:8081"
$username = "18755869972"
$password = "tcc123698741"

Write-Host "=== 测试Java自动预约系统 ===" -ForegroundColor Green

# 1. 登录获取token
Write-Host "1. 正在登录用户: $username" -ForegroundColor Yellow
try {
    $loginBody = @{
        username = $username
        password = $password
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    $token = $loginResponse.data.token
    Write-Host "登录成功，获取到token" -ForegroundColor Green
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
} catch {
    Write-Host "登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 获取用户信息
Write-Host "2. 获取用户信息" -ForegroundColor Yellow
try {
    $userInfo = Invoke-RestMethod -Uri "$baseUrl/api/users/info?username=$username" -Method Get -Headers $headers
    Write-Host "用户ID: $($userInfo.data.id)" -ForegroundColor Cyan
    Write-Host "剩余天数: $($userInfo.data.remainingDays)" -ForegroundColor Cyan
} catch {
    Write-Host "获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 查看现有自动预约
Write-Host "3. 查看现有自动预约" -ForegroundColor Yellow
try {
    $existingReservations = Invoke-RestMethod -Uri "$baseUrl/api/reservations/auto" -Method Get -Headers $headers
    Write-Host "现有自动预约数量: $($existingReservations.data.Count)" -ForegroundColor Cyan
    foreach ($reservation in $existingReservations.data) {
        Write-Host "  预约ID: $($reservation.id), 状态: $($reservation.status), 房间: $($reservation.roomId), 座位: $($reservation.seatId)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "获取自动预约失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 创建新的自动预约（基于config.json中的配置）
Write-Host "4. 创建新的自动预约" -ForegroundColor Yellow
try {
    $newReservation = @{
        roomId = 4991
        seatId = "005"
        startTime = "12:30"
        endTime = "22:00"
        reservationOpenTime = "08:00:00"
        reservationType = "SAME_DAY"
    } | ConvertTo-Json

    $createResponse = Invoke-RestMethod -Uri "$baseUrl/api/reservations/auto" -Method Post -Body $newReservation -Headers $headers
    Write-Host "创建自动预约成功，预约ID: $($createResponse.data.id)" -ForegroundColor Green
    $reservationId = $createResponse.data.id
} catch {
    Write-Host "创建自动预约失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "错误详情: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 5. 手动触发执行（如果创建成功）
if ($reservationId) {
    Write-Host "5. 手动触发自动预约执行" -ForegroundColor Yellow
    try {
        $executeResponse = Invoke-RestMethod -Uri "$baseUrl/api/reservations/auto/$reservationId/execute" -Method Post -Headers $headers
        Write-Host "手动执行触发成功: $($executeResponse.message)" -ForegroundColor Green
        
        # 等待几秒钟让执行完成
        Start-Sleep -Seconds 5
        
        # 查看执行结果
        Write-Host "6. 查看执行结果" -ForegroundColor Yellow
        $updatedReservation = Invoke-RestMethod -Uri "$baseUrl/api/reservations/auto" -Method Get -Headers $headers
        foreach ($reservation in $updatedReservation.data) {
            if ($reservation.id -eq $reservationId) {
                Write-Host "  预约ID: $($reservation.id)" -ForegroundColor Cyan
                Write-Host "  状态: $($reservation.status)" -ForegroundColor Cyan
                Write-Host "  最后执行时间: $($reservation.lastExecutionTime)" -ForegroundColor Cyan
                Write-Host "  执行结果: $($reservation.executionResult)" -ForegroundColor Cyan
                break
            }
        }
    } catch {
        Write-Host "手动执行失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "=== 测试完成 ===" -ForegroundColor Green
