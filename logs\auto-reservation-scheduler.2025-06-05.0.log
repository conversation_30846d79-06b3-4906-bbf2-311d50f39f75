2025-06-05 15:49:00.008  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:49:00.009 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:49:00 的待执行预约
2025-06-05 15:49:00.349 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 15:50:00.009  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:50:00.010 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:50:00 的待执行预约
2025-06-05 15:50:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:16:00.002  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:16:00.002 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:16:00 的待执行预约
2025-06-05 21:16:00.609 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:16:22.891  INFO --- [nio-8081-exec-7] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 21:16:43.893  INFO --- [nio-8081-exec-5] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 21:16:43.905  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 21:16:45.365 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 21:16:45.468 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor14.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 21:16:45.471  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 21:17:00.016  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:17:00.016 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:17:00 的待执行预约
2025-06-05 21:17:00.020 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:18:00.012  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:18:00.013 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:18:00 的待执行预约
2025-06-05 21:18:00.020 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:19:00.014  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:19:00.015 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:19:00 的待执行预约
2025-06-05 21:19:00.020 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:20:00.006  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:20:00.006 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:20:00 的待执行预约
2025-06-05 21:20:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:21:00.016  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:21:00.017 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:21:00 的待执行预约
2025-06-05 21:21:00.021 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:22:00.004  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:22:00.005 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:22:00 的待执行预约
2025-06-05 21:22:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:23:00.013  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:23:00.013 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:23:00 的待执行预约
2025-06-05 21:23:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:51:00.010  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:51:00.012 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:51:00 的待执行预约
2025-06-05 21:51:00.615 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:51:40.429  INFO --- [nio-8081-exec-6] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 21:51:40.451  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 21:51:42.171 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 21:51:42.256 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor16.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 21:51:42.262  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 21:52:00.016  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:52:00.023 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:52:00 的待执行预约
2025-06-05 21:52:00.034 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:53:00.014  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:53:00.015 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:53:00 的待执行预约
2025-06-05 21:53:00.018 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:54:00.008  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:54:00.008 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:54:00 的待执行预约
2025-06-05 21:54:00.015 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:55:00.009  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:55:00.009 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:55:00 的待执行预约
2025-06-05 21:55:00.013 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:56:00.009  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:56:00.009 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:56:00 的待执行预约
2025-06-05 21:56:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:57:00.013  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:57:00.014 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:57:00 的待执行预约
2025-06-05 21:57:00.024 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:11:00.003  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:11:00.007 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:11:00 的待执行预约
2025-06-05 22:11:00.987 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:12:00.008  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:12:00.009 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:12:00 的待执行预约
2025-06-05 22:12:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:13:00.005  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:13:00.006 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:13:00 的待执行预约
2025-06-05 22:13:00.016 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:14:00.000  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:14:00.002 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:14:00 的待执行预约
2025-06-05 22:14:00.015  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:14:00.018 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:14:00 的待执行预约
2025-06-05 22:14:00.029 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:14:00.638 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:14:09.420  INFO --- [nio-8081-exec-7] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 22:14:09.439  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=005
2025-06-05 22:14:10.795 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 22:14:10.873 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 22:14:10.878  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 22:15:00.010  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:15:00.010 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:15:00 的待执行预约
2025-06-05 22:15:00.010  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:15:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:15:00 的待执行预约
2025-06-05 22:15:00.021 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:15:00.025 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:16:00.003  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:16:00.003  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:16:00.003 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:16:00 的待执行预约
2025-06-05 22:16:00.003 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:16:00 的待执行预约
2025-06-05 22:16:00.013 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:16:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:17:00.008  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:17:00.008  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:17:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:17:00 的待执行预约
2025-06-05 22:17:00.009 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:17:00 的待执行预约
2025-06-05 22:17:00.018 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:17:00.018 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:18:00.014  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:18:00.014  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:18:00.015 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:18:00 的待执行预约
2025-06-05 22:18:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:18:00 的待执行预约
2025-06-05 22:18:00.021 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:18:00.022 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:19:00.012  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:19:00.012  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:19:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:19:00 的待执行预约
2025-06-05 22:19:00.012 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:19:00 的待执行预约
2025-06-05 22:19:00.018 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:19:00.018 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:20:00.010  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:20:00.010  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:20:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:20:00 的待执行预约
2025-06-05 22:20:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:20:00 的待执行预约
2025-06-05 22:20:00.016 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:20:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:21:00.015  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:21:00.015  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:21:00.015 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:21:00 的待执行预约
2025-06-05 22:21:00.015 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:21:00 的待执行预约
2025-06-05 22:21:00.027 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:21:00.029 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:21:20.258  INFO --- [nio-8081-exec-7] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 22:21:20.263  INFO --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=005
2025-06-05 22:21:21.266 DEBUG --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 22:21:21.270 ERROR --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 22:21:21.273  INFO --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 22:22:00.001  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:22:00.001  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:22:00.002 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:22:00 的待执行预约
2025-06-05 22:22:00.002 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:22:00 的待执行预约
2025-06-05 22:22:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:22:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:23:00.009  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:23:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:23:00 的待执行预约
2025-06-05 22:23:00.014 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:23:15.252  INFO --- [nio-8081-exec-7] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 22:23:15.258  INFO --- [pool-1-thread-3] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=005
2025-06-05 22:23:16.259 DEBUG --- [pool-1-thread-3] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 22:23:16.265 ERROR --- [pool-1-thread-3] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 22:23:16.267  INFO --- [pool-1-thread-3] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 22:24:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:24:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:24:00 的待执行预约
2025-06-05 22:24:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:25:00.014  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:25:00.015 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:25:00 的待执行预约
2025-06-05 22:25:00.025 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:26:00.011  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:26:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:26:00 的待执行预约
2025-06-05 22:26:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:27:00.011  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:27:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:27:00 的待执行预约
2025-06-05 22:27:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:28:00.014  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:28:00.014 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:28:00 的待执行预约
2025-06-05 22:28:00.022 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:29:00.002  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:29:00.002 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:29:00 的待执行预约
2025-06-05 22:29:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:30:00.005  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:30:00.005 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:30:00 的待执行预约
2025-06-05 22:30:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:31:00.004  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:31:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:31:00 的待执行预约
2025-06-05 22:31:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:32:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:32:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:32:00 的待执行预约
2025-06-05 22:32:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:33:00.010  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:33:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:33:00 的待执行预约
2025-06-05 22:33:00.016 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:34:00.006  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:34:00.007 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:34:00 的待执行预约
2025-06-05 22:34:00.011 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:35:00.001  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:35:00.002 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:35:00 的待执行预约
2025-06-05 22:35:00.006 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:36:00.008  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:36:00.008 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:36:00 的待执行预约
2025-06-05 22:36:00.014 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:37:00.001  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:37:00.001 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:37:00 的待执行预约
2025-06-05 22:37:00.005 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:38:00.000  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:38:00.000 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:38:00 的待执行预约
2025-06-05 22:38:00.008 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:39:00.010  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:39:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:39:00 的待执行预约
2025-06-05 22:39:00.019 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:40:00.015  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:40:00.017 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:40:00 的待执行预约
2025-06-05 22:40:00.026 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:41:00.004  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:41:00.005 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:41:00 的待执行预约
2025-06-05 22:41:00.013 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:42:00.006  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:42:00.008 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:42:00 的待执行预约
2025-06-05 22:42:00.019 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:43:00.011  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:43:00.011 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:43:00 的待执行预约
2025-06-05 22:43:00.019 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:44:00.004  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:44:00.004 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:44:00 的待执行预约
2025-06-05 22:44:00.015 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:45:00.012  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:45:00.012 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:45:00 的待执行预约
2025-06-05 22:45:00.017 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:46:00.012  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:46:00.012 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:46:00 的待执行预约
2025-06-05 22:46:00.019 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:47:00.014  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:47:00.015 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:47:00 的待执行预约
2025-06-05 22:47:00.023 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:48:00.010  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:48:00.011 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:48:00 的待执行预约
2025-06-05 22:48:00.029 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:49:00.002  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:49:00.003 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:49:00 的待执行预约
2025-06-05 22:49:00.013 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:50:00.006  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:50:00.006 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:50:00 的待执行预约
2025-06-05 22:50:00.014 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:51:00.001  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:51:00.001 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:51:00 的待执行预约
2025-06-05 22:51:00.011 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:52:00.013  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:52:00.013 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:52:00 的待执行预约
2025-06-05 22:52:00.021 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:53:00.004  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:53:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:53:00 的待执行预约
2025-06-05 22:53:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:54:00.008  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:54:00.008 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:54:00 的待执行预约
2025-06-05 22:54:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:55:00.012  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:55:00.013 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:55:00 的待执行预约
2025-06-05 22:55:00.020 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:56:00.005  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:56:00.006 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:56:00 的待执行预约
2025-06-05 22:56:00.057 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:57:00.009  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:57:00.010 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:57:00 的待执行预约
2025-06-05 22:57:00.020 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:58:00.014  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:58:00.014 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:58:00 的待执行预约
2025-06-05 22:58:00.025 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:59:00.010  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:59:00.011 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:59:00 的待执行预约
2025-06-05 22:59:00.017 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 23:00:00.014  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 23:00:00.017 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 23:00:00 的待执行预约
2025-06-05 23:00:00.026 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
