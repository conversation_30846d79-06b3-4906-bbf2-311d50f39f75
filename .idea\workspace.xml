<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c29346b2-ecad-4bb8-95d3-8afce5745df5" name="变更" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/libraries/Maven__commons_codec_commons_codec_1_15.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/libraries/Maven__org_apache_httpcomponents_httpclient_4_5_14.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/libraries/Maven__org_apache_httpcomponents_httpcore_4_4_16.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/sqldialects.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/seat-reservation-backend.iml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/seat-reservation-backend.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/SeatReservationApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/SeatReservationApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/controller/ReservationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/controller/ReservationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/entity/Reservation.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/entity/Reservation.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/ReservationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/ReservationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/UserService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/UserService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/impl/ReservationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/impl/ReservationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/impl/UserServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/seatmaster/service/impl/UserServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/SeatReservationApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/SeatReservationApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/controller/ReservationController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/controller/ReservationController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/entity/Reservation.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/entity/Reservation.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/ReservationService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/ReservationService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/UserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/UserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/impl/ReservationServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/impl/ReservationServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/impl/UserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/seatmaster/service/impl/UserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/seat-reservation-backend-1.0.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/seat-reservation-backend-1.0.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/seat-reservation-backend-1.0.0.jar.original" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\.m2\respository" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2xnkJua0dMxecQXSOHixMw8ij77" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "WebServerToolWindowFactoryState": "false",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "MavenSettings",
    "spring.configuration.checksum": "582604c12287aa3e4f084a505ae7a520",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Spring Boot.SeatReservationApplication">
    <configuration name="generated-requests | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" requestIdentifier="#1" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #2" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="2" requestIdentifier="#2" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #3" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="3" requestIdentifier="#3" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="SeatReservationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="seat-reservation-backend" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.seatmaster.SeatReservationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP 请求.generated-requests | #3" />
        <item itemvalue="HTTP 请求.generated-requests | #1" />
        <item itemvalue="HTTP 请求.generated-requests | #2" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c29346b2-ecad-4bb8-95d3-8afce5745df5" name="变更" comment="" />
      <created>1748581162368</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748581162368</updated>
      <workItem from="1748581164087" duration="11155000" />
      <workItem from="1748742672188" duration="16366000" />
      <workItem from="1748822897705" duration="5698000" />
      <workItem from="1748931219642" duration="7488000" />
      <workItem from="1749032683995" duration="4014000" />
      <workItem from="1749109666770" duration="1801000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>