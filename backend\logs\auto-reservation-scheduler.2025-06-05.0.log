2025-06-05 15:40:00.011  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:40:00.021 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:40:00 的待执行预约
2025-06-05 15:40:00.347 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 15:41:00.005  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:41:00.007 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:41:00 的待执行预约
2025-06-05 15:41:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 15:42:00.001  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:42:00.003 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:42:00 的待执行预约
2025-06-05 15:42:00.010 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 15:43:00.010  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:43:00.011 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:43:00 的待执行预约
2025-06-05 15:43:00.028  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 找到 1 个待执行的自动预约任务
2025-06-05 15:43:00.034  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 已提交 1 个自动预约任务执行
2025-06-05 15:43:00.035  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 15:43:00.835 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 15:43:00.914 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy71.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy85.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$scanAndExecuteAutoReservations$0(AutoReservationSchedulerService.java:91)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1800)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:630)
	at java.base/java.lang.Thread.run(Thread.java:832)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy95.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 15:43:00.918  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 15:44:00.007  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:44:00.008 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:44:00 的待执行预约
2025-06-05 15:44:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 15:46:00.007  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:46:00.009 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:46:00 的待执行预约
2025-06-05 15:46:00.301 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 15:47:00.014  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 15:47:00.017 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 15:47:00 的待执行预约
2025-06-05 15:47:00.298 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 16:04:00.010  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:04:00.012 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:04:00 的待执行预约
2025-06-05 16:04:00.418 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 16:05:00.002  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:05:00.005 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:05:00 的待执行预约
2025-06-05 16:05:00.019  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 找到 1 个待执行的自动预约任务
2025-06-05 16:05:00.021  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 已提交 1 个自动预约任务执行
2025-06-05 16:05:00.022  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 16:05:00.742 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 16:05:00.823 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy71.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy85.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$scanAndExecuteAutoReservations$0(AutoReservationSchedulerService.java:91)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1800)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:630)
	at java.base/java.lang.Thread.run(Thread.java:832)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy95.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 16:05:00.827  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 16:06:00.012  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:06:00.013 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:06:00 的待执行预约
2025-06-05 16:06:00.018 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 16:07:00.008  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:07:00.009 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:07:00 的待执行预约
2025-06-05 16:07:00.015 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 16:08:00.006  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:08:00.007 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:08:00 的待执行预约
2025-06-05 16:08:00.011 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 16:09:00.012  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:09:00.013 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:09:00 的待执行预约
2025-06-05 16:09:00.020 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 16:10:00.004  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 16:10:00.005 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 16:10:00 的待执行预约
2025-06-05 16:10:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:03:16.499  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:03:16.516 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:03:16 的待执行预约
2025-06-05 20:03:16.599 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:04:00.006  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:04:00.007 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:04:00 的待执行预约
2025-06-05 20:04:00.096 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:05:00.013  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:05:00.014 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:05:00 的待执行预约
2025-06-05 20:05:00.032 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:06:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:06:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:06:00 的待执行预约
2025-06-05 20:06:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:07:00.003  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:07:00.005 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:07:00 的待执行预约
2025-06-05 20:07:00.320 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:08:00.008  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:08:00.008 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:08:00 的待执行预约
2025-06-05 20:08:00.025  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 找到 1 个待执行的自动预约任务
2025-06-05 20:08:00.028  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 已提交 1 个自动预约任务执行
2025-06-05 20:08:00.028  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 20:08:00.788 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 20:08:00.853 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy71.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy85.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$scanAndExecuteAutoReservations$0(AutoReservationSchedulerService.java:91)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1800)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:630)
	at java.base/java.lang.Thread.run(Thread.java:832)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy95.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 20:08:00.856  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 20:09:00.010  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:09:00.011 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:09:00 的待执行预约
2025-06-05 20:09:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:10:00.013  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:10:00.014 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:10:00 的待执行预约
2025-06-05 20:10:00.021 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:11:00.006  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:11:00.006 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:11:00 的待执行预约
2025-06-05 20:11:00.010 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:12:00.004  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:12:00.005 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:12:00 的待执行预约
2025-06-05 20:12:00.010 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:13:00.005  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:13:00.005 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:13:00 的待执行预约
2025-06-05 20:13:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:14:00.014  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:14:00.015 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:14:00 的待执行预约
2025-06-05 20:14:00.023 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:15:00.010  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:15:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:15:00 的待执行预约
2025-06-05 20:15:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:16:00.001  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:16:00.002 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:16:00 的待执行预约
2025-06-05 20:16:00.007 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:17:00.009  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:17:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:17:00 的待执行预约
2025-06-05 20:17:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:18:00.002  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:18:00.002 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:18:00 的待执行预约
2025-06-05 20:18:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:20:00.015  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:20:00.016 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:20:00 的待执行预约
2025-06-05 20:20:00.345 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:21:00.010  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:21:00.012 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:21:00 的待执行预约
2025-06-05 20:21:00.021 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:22:00.010  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:22:00.011 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:22:00 的待执行预约
2025-06-05 20:22:00.017 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:23:00.011  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:23:00.013 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:23:00 的待执行预约
2025-06-05 20:23:00.018 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:24:00.014  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:24:00.015 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:24:00 的待执行预约
2025-06-05 20:24:00.021 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:25:00.011  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:25:00.011 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:25:00 的待执行预约
2025-06-05 20:25:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:26:00.008  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:26:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:26:00 的待执行预约
2025-06-05 20:26:00.023  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 找到 1 个待执行的自动预约任务
2025-06-05 20:26:00.026  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 已提交 1 个自动预约任务执行
2025-06-05 20:26:00.026  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 20:26:01.469 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 20:26:01.561 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy71.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy85.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$scanAndExecuteAutoReservations$0(AutoReservationSchedulerService.java:91)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1800)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:630)
	at java.base/java.lang.Thread.run(Thread.java:832)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy95.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 20:26:01.566  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 20:27:00.007  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:27:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:27:00 的待执行预约
2025-06-05 20:27:00.015 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:28:00.008  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:28:00.010 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:28:00 的待执行预约
2025-06-05 20:28:00.018 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:29:00.011  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:29:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:29:00 的待执行预约
2025-06-05 20:29:00.019 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:30:00.005  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:30:00.006 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:30:00 的待执行预约
2025-06-05 20:30:00.012  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 找到 1 个待执行的自动预约任务
2025-06-05 20:30:00.012  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 已提交 1 个自动预约任务执行
2025-06-05 20:30:00.012  INFO --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 20:30:01.034 DEBUG --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 20:30:01.040 ERROR --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy71.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy85.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$scanAndExecuteAutoReservations$0(AutoReservationSchedulerService.java:91)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1800)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:630)
	at java.base/java.lang.Thread.run(Thread.java:832)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor13.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy95.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 20:30:01.043  INFO --- [pool-1-thread-2] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 20:31:00.005  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:31:00.006 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:31:00 的待执行预约
2025-06-05 20:31:00.011 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:32:00.014  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:32:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:32:00 的待执行预约
2025-06-05 20:32:00.023 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:33:00.007  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:33:00.008 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:33:00 的待执行预约
2025-06-05 20:33:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:35:00.018  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:35:00.020 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:35:00 的待执行预约
2025-06-05 20:35:00.354 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:36:00.011  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:36:00.014 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:36:00 的待执行预约
2025-06-05 20:36:00.024 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:37:00.016  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:37:00.017 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:37:00 的待执行预约
2025-06-05 20:37:00.024 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:38:00.008  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:38:00.008 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:38:00 的待执行预约
2025-06-05 20:38:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:39:00.008  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:39:00.009 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:39:00 的待执行预约
2025-06-05 20:39:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:40:00.013  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:40:00.013 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:40:00 的待执行预约
2025-06-05 20:40:00.020 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:41:00.012  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:41:00.013 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:41:00 的待执行预约
2025-06-05 20:41:00.019 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:42:00.004  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:42:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:42:00 的待执行预约
2025-06-05 20:42:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:43:00.002  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:43:00.003 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:43:00 的待执行预约
2025-06-05 20:43:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:44:00.007  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:44:00.007 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:44:00 的待执行预约
2025-06-05 20:44:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:45:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:45:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:45:00 的待执行预约
2025-06-05 20:45:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:46:00.005  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:46:00.006 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:46:00 的待执行预约
2025-06-05 20:46:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:47:00.013  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:47:00.014 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:47:00 的待执行预约
2025-06-05 20:47:00.020 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:48:00.011  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:48:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:48:00 的待执行预约
2025-06-05 20:48:00.017 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:49:00.015  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:49:00.016 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:49:00 的待执行预约
2025-06-05 20:49:00.021 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:50:00.011  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:50:00.012 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:50:00 的待执行预约
2025-06-05 20:50:00.021 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:51:00.009  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:51:00.048 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:51:00 的待执行预约
2025-06-05 20:51:00.391 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 20:52:00.000  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:52:00.001 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:52:00 的待执行预约
2025-06-05 20:52:00.017  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 找到 1 个待执行的自动预约任务
2025-06-05 20:52:00.021  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 已提交 1 个自动预约任务执行
2025-06-05 20:52:00.021  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 20:52:01.523 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 20:52:01.581 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy71.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy85.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$scanAndExecuteAutoReservations$0(AutoReservationSchedulerService.java:91)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1800)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:630)
	at java.base/java.lang.Thread.run(Thread.java:832)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy95.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:564)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 20:52:01.584  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 20:53:00.014  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 20:53:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 20:53:00 的待执行预约
2025-06-05 20:53:00.018 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:14:00.005  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:14:00.007 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:14:00 的待执行预约
2025-06-05 21:14:00.477 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:26:00.002  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:26:00.007 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:26:00 的待执行预约
2025-06-05 21:26:01.080 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:26:27.540  INFO --- [nio-8081-exec-7] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=46, userId=20
2025-06-05 21:26:27.550  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 21:26:28.985 DEBUG --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=46, status=AUTO_FAILED
2025-06-05 21:26:29.050 ERROR --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=46, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor16.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-05 21:26:29.053  INFO --- [pool-1-thread-1] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=46, success=false, message=座位预约失败
2025-06-05 21:27:00.004  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:27:00.006 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:27:00 的待执行预约
2025-06-05 21:27:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:28:00.005  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:28:00.006 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:28:00 的待执行预约
2025-06-05 21:28:00.014 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:29:00.003  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:29:00.005 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:29:00 的待执行预约
2025-06-05 21:29:00.010 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:30:00.001  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:30:00.001 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:30:00 的待执行预约
2025-06-05 21:30:00.007 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:31:00.012  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:31:00.012 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:31:00 的待执行预约
2025-06-05 21:31:00.018 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:32:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:32:00.003 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:32:00 的待执行预约
2025-06-05 21:32:00.007 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:33:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:33:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:33:00 的待执行预约
2025-06-05 21:33:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:34:00.001  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:34:00.001 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:34:00 的待执行预约
2025-06-05 21:34:00.006 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:35:00.005  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:35:00.005 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:35:00 的待执行预约
2025-06-05 21:35:00.013 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:36:00.002  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:36:00.003 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:36:00 的待执行预约
2025-06-05 21:36:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:37:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:37:00.003 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:37:00 的待执行预约
2025-06-05 21:37:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:38:00.012  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:38:00.013 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:38:00 的待执行预约
2025-06-05 21:38:00.019 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:39:00.001  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:39:00.002 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:39:00 的待执行预约
2025-06-05 21:39:00.007 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:40:00.010  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:40:00.010 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:40:00 的待执行预约
2025-06-05 21:40:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:41:00.008  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:41:00.009 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:41:00 的待执行预约
2025-06-05 21:41:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:42:00.010  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:42:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:42:00 的待执行预约
2025-06-05 21:42:00.018 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:43:00.005  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:43:00.005 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:43:00 的待执行预约
2025-06-05 21:43:00.009 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:44:00.003  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:44:00.004 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:44:00 的待执行预约
2025-06-05 21:44:00.009 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:45:00.013  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:45:00.014 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:45:00 的待执行预约
2025-06-05 21:45:00.020 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:46:00.009  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:46:00.010 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:46:00 的待执行预约
2025-06-05 21:46:00.017 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:47:00.014  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:47:00.014 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:47:00 的待执行预约
2025-06-05 21:47:00.018 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 21:48:00.006  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 21:48:00.006 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 21:48:00 的待执行预约
2025-06-05 21:48:00.015 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:23:00.009  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:23:00.009 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:23:00 的待执行预约
2025-06-05 22:23:00.548 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:24:00.003  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:24:00.004 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:24:00 的待执行预约
2025-06-05 22:24:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:25:00.014  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:25:00.015 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:25:00 的待执行预约
2025-06-05 22:25:00.025 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:26:00.011  INFO --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:26:00.011 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:26:00 的待执行预约
2025-06-05 22:26:00.021 DEBUG --- [   scheduling-5] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:27:00.011  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:27:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:27:00 的待执行预约
2025-06-05 22:27:00.041 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:28:00.014  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:28:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:28:00 的待执行预约
2025-06-05 22:28:00.022 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:29:00.002  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:29:00.002 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:29:00 的待执行预约
2025-06-05 22:29:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:30:00.005  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:30:00.005 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:30:00 的待执行预约
2025-06-05 22:30:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:31:00.004  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:31:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:31:00 的待执行预约
2025-06-05 22:31:00.008 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:32:00.003  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:32:00.004 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:32:00 的待执行预约
2025-06-05 22:32:00.009 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:33:00.010  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:33:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:33:00 的待执行预约
2025-06-05 22:33:00.020 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:34:00.006  INFO --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:34:00.006 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:34:00 的待执行预约
2025-06-05 22:34:00.011 DEBUG --- [   scheduling-7] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:35:00.016  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:35:00.018 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:35:00 的待执行预约
2025-06-05 22:35:00.023 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:36:00.008  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:36:00.008 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:36:00 的待执行预约
2025-06-05 22:36:00.014 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:37:00.001  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:37:00.001 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:37:00 的待执行预约
2025-06-05 22:37:00.008 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:38:00.000  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:38:00.002 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:38:00 的待执行预约
2025-06-05 22:38:00.015 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:39:00.010  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:39:00.011 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:39:00 的待执行预约
2025-06-05 22:39:00.025 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:40:00.002  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:40:00.003 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:40:00 的待执行预约
2025-06-05 22:40:00.014 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:41:00.004  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:41:00.005 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:41:00 的待执行预约
2025-06-05 22:41:00.012 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:42:00.006  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:42:00.007 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:42:00 的待执行预约
2025-06-05 22:42:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:43:00.011  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:43:00.011 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:43:00 的待执行预约
2025-06-05 22:43:00.019 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:44:00.004  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:44:00.004 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:44:00 的待执行预约
2025-06-05 22:44:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:45:00.012  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:45:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:45:00 的待执行预约
2025-06-05 22:45:00.017 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:46:00.012  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:46:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:46:00 的待执行预约
2025-06-05 22:46:00.021 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:47:00.014  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:47:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:47:00 的待执行预约
2025-06-05 22:47:00.025 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:48:00.010  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:48:00.011 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:48:00 的待执行预约
2025-06-05 22:48:00.018 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:49:00.002  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:49:00.003 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:49:00 的待执行预约
2025-06-05 22:49:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:50:00.006  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:50:00.006 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:50:00 的待执行预约
2025-06-05 22:50:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:51:00.016  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:51:00.017 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:51:00 的待执行预约
2025-06-05 22:51:00.027 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:52:00.013  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:52:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:52:00 的待执行预约
2025-06-05 22:52:00.021 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:53:00.004  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:53:00.004 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:53:00 的待执行预约
2025-06-05 22:53:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:54:00.008  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:54:00.008 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:54:00 的待执行预约
2025-06-05 22:54:00.016 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:55:00.012  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:55:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:55:00 的待执行预约
2025-06-05 22:55:00.020 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:56:00.005  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:56:00.006 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:56:00 的待执行预约
2025-06-05 22:56:00.015 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:57:00.009  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:57:00.011 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:57:00 的待执行预约
2025-06-05 22:57:00.031 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:58:00.014  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:58:00.014 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:58:00 的待执行预约
2025-06-05 22:58:00.027 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 22:59:00.010  INFO --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 22:59:00.011 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 查询当前时间 22:59:00 的待执行预约
2025-06-05 22:59:00.021 DEBUG --- [  scheduling-10] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-05 23:00:00.014  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-05 23:00:00.014 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 23:00:00 的待执行预约
2025-06-05 23:00:00.026 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
