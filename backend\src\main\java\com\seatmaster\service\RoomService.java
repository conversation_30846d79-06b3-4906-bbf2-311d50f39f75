package com.seatmaster.service;

import com.seatmaster.entity.Room;

import java.util.List;

public interface RoomService {
    
    /**
     * 获取所有房间列表
     */
    List<Room> getAllRooms();
    
    /**
     * 根据学校ID获取房间列表
     */
    List<Room> getRoomsBySchoolId(Long schoolId);
    
    /**
     * 根据ID获取房间
     */
    Room getRoomById(Long id);

    /**
     * 根据room_id字段获取房间
     */
    Room getRoomByRoomId(String roomId);
    
    /**
     * 创建房间
     */
    Room createRoom(Room room);
    
    /**
     * 更新房间
     */
    Room updateRoom(Room room);
    
    /**
     * 删除房间
     */
    boolean deleteRoom(Long id);
}
