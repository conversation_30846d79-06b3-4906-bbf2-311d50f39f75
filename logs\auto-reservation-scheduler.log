2025-06-06 09:24:35.983  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:24:36.067 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:24:36 的待执行预约
2025-06-06 09:24:36.068 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:25:00.015  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:25:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:25:00 的待执行预约
2025-06-06 09:25:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:26:00.013  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:26:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:26:00 的待执行预约
2025-06-06 09:26:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:27:00.015  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:27:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:27:00 的待执行预约
2025-06-06 09:27:00.026 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:28:00.013  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:28:00.016 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:28:00 的待执行预约
2025-06-06 09:28:00.022 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:29:00.011  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:29:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:29:00 的待执行预约
2025-06-06 09:29:00.015 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:30:00.013  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:30:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:30:00 的待执行预约
2025-06-06 09:30:00.023 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:31:00.015  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:31:00.015 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:31:00 的待执行预约
2025-06-06 09:31:00.029 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:32:00.012  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:32:00.013 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:32:00 的待执行预约
2025-06-06 09:32:00.023 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:33:00.009  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:33:00.012 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:33:00 的待执行预约
2025-06-06 09:33:00.012 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:34:00.012  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:34:00.013 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:34:00 的待执行预约
2025-06-06 09:34:00.013 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:35:00.010  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:35:00.010 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:35:00 的待执行预约
2025-06-06 09:35:00.017 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:36:00.010  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:36:00.011 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:36:00 的待执行预约
2025-06-06 09:36:00.011 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:37:00.023  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:37:00.026 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:37:00 的待执行预约
2025-06-06 09:37:00.026 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:38:00.009  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:38:00.009 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:38:00 的待执行预约
2025-06-06 09:38:00.009 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:39:00.007  INFO --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:39:00.008 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:39:00 的待执行预约
2025-06-06 09:39:00.015 DEBUG --- [   scheduling-9] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:40:00.007  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:40:00.007 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:40:00 的待执行预约
2025-06-06 09:40:00.014 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:41:00.007  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:41:00.009 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:41:00 的待执行预约
2025-06-06 09:41:00.009 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:42:00.022  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:42:00.022 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:42:00 的待执行预约
2025-06-06 09:42:00.030 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:43:00.004  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:43:00.005 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:43:00 的待执行预约
2025-06-06 09:43:00.012 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:44:00.003  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:44:00.007 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:44:00 的待执行预约
2025-06-06 09:44:00.015 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:45:00.001  INFO --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:45:00.002 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:45:00 的待执行预约
2025-06-06 09:45:00.004 DEBUG --- [   scheduling-4] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:46:00.011  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:46:00.013 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:46:00 的待执行预约
2025-06-06 09:46:00.034 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:47:00.015  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:47:00.016 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:47:00 的待执行预约
2025-06-06 09:47:00.035 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:48:00.001  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:48:00.002 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:48:00 的待执行预约
2025-06-06 09:48:00.004 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:49:00.017  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:49:00.018 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:49:00 的待执行预约
2025-06-06 09:49:00.024 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:50:00.016  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:50:00.018 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:50:00 的待执行预约
2025-06-06 09:50:00.026 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:51:00.002  INFO --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:51:00.002 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:51:00 的待执行预约
2025-06-06 09:51:00.011 DEBUG --- [   scheduling-2] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:52:00.016  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:52:00.018 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:52:00 的待执行预约
2025-06-06 09:52:00.025 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:53:00.013  INFO --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:53:00.014 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:53:00 的待执行预约
2025-06-06 09:53:00.022 DEBUG --- [   scheduling-1] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:54:00.014  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:54:00.015 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:54:00 的待执行预约
2025-06-06 09:54:00.025 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:55:00.016  INFO --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:55:00.018 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:55:00 的待执行预约
2025-06-06 09:55:00.024 DEBUG --- [   scheduling-6] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:56:00.013  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:56:00.014 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:56:00 的待执行预约
2025-06-06 09:56:00.021 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:56:14.353  INFO --- [nio-8081-exec-7] c.s.s.AutoReservationSchedulerService : 手动触发自动预约: reservationId=50, userId=20
2025-06-06 09:56:14.359  INFO --- [pool-1-thread-4] c.s.s.AutoReservationSchedulerService : 开始执行自动预约: reservationId=50, userId=20, roomId=4991, seatId=005
2025-06-06 09:56:14.598 DEBUG --- [pool-1-thread-4] c.s.s.AutoReservationSchedulerService : 更新预约状态成功: reservationId=50, status=AUTO_FAILED
2025-06-06 09:56:14.598 ERROR --- [pool-1-thread-4] c.s.s.AutoReservationSchedulerService : 记录执行日志失败: reservationId=50, error=
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
### The error may exist in com/seatmaster/mapper/AutoExecutionLogMapper.java (best guess)
### The error may involve com.seatmaster.mapper.AutoExecutionLogMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO auto_execution_logs  ( reservation_id, user_id, execution_time, execution_status, result_message, execution_duration, created_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ? )
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy70.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy84.insert(Unknown Source)
	at com.seatmaster.service.AutoReservationSchedulerService.recordExecutionLog(AutoReservationSchedulerService.java:224)
	at com.seatmaster.service.AutoReservationSchedulerService.executeAutoReservation(AutoReservationSchedulerService.java:169)
	at com.seatmaster.service.AutoReservationSchedulerService.lambda$manualExecuteReservation$2(AutoReservationSchedulerService.java:259)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'created_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at jdk.internal.reflect.GeneratedMethodAccessor15.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy94.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 13 common frames omitted
2025-06-06 09:56:14.608  INFO --- [pool-1-thread-4] c.s.s.AutoReservationSchedulerService : 自动预约执行完成: reservationId=50, success=false, message=房间信息错误
2025-06-06 09:57:00.012  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:57:00.013 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:57:00 的待执行预约
2025-06-06 09:57:00.019 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:58:00.011  INFO --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:58:00.013 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:58:00 的待执行预约
2025-06-06 09:58:00.018 DEBUG --- [   scheduling-8] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 09:59:00.010  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 09:59:00.011 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 09:59:00 的待执行预约
2025-06-06 09:59:00.013 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
2025-06-06 10:00:00.008  INFO --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 开始扫描自动预约任务...
2025-06-06 10:00:00.009 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 查询当前时间 10:00:00 的待执行预约
2025-06-06 10:00:00.012 DEBUG --- [   scheduling-3] c.s.s.AutoReservationSchedulerService : 没有找到待执行的自动预约任务
