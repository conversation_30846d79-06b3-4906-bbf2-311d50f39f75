2025-06-05 21:16:43.943  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始执行学习通预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 21:16:43.943  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约时间详情: startTime=15:30, endTime=20:30, openTime=20:52:00, type=SAME_DAY
2025-06-05 21:16:43.946 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤1: 获取用户信息, userId=20
2025-06-05 21:16:43.952  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 用户信息获取成功: username=18755869972, name=预约陶承程, remainingDays=3
2025-06-05 21:16:43.954 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤2: 检查用户剩余天数, remainingDays=3
2025-06-05 21:16:43.954 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤3: 创建学习通会话
2025-06-05 21:16:43.956 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤4: 开始登录学习通, username=18755869972
2025-06-05 21:16:43.957  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始登录学习通: username=18755869972
2025-06-05 21:16:43.958 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤1: 获取登录页面，建立会话
2025-06-05 21:16:44.406 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 提取到 2 个Cookie
2025-06-05 21:16:44.406 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取登录页面成功
2025-06-05 21:16:44.407 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录页面获取完成，当前Cookie数量: 2
2025-06-05 21:16:44.407 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤2: 构建登录参数
2025-06-05 21:16:44.408 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录参数构建完成: fid=-1, refer=office.chaoxing.com
2025-06-05 21:16:44.408 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤3: 发送登录请求到 https://passport2.chaoxing.com/fanyalogin
2025-06-05 21:16:44.467 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录请求响应: status=200 OK, bodyLength=93
2025-06-05 21:16:44.468 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤4: 解析登录结果
2025-06-05 21:16:44.468 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录响应内容: {"url":"http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode","status":true}
2025-06-05 21:16:44.472 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 提取到 12 个Cookie
2025-06-05 21:16:44.472  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972, 总Cookie数量=12
2025-06-05 21:16:44.472  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972
2025-06-05 21:16:44.473 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤5: 获取实际的room_id
2025-06-05 21:16:44.477  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取到实际room_id: tableId=64, actualRoomId=10347
2025-06-05 21:16:44.477 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤6: 开始执行预约, actualRoomId=10347, seatId=001
2025-06-05 21:16:44.478  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始提交预约: roomId=10347, seatId=001, startTime=15:30, endTime=20:30
2025-06-05 21:16:44.478 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤1: 获取页面token
2025-06-05 21:16:44.480  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取页面token: url=https://office.chaoxing.com/front/third/apps/seat/code?id=10347&seatNum=001
2025-06-05 21:16:44.480 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 发送token页面请求，当前Cookie数量: 12
2025-06-05 21:16:45.347 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : token页面响应: status=200 OK, bodyLength=3803
2025-06-05 21:16:45.348 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取到的HTML内容前1000字符: <!DOCTYPE html>
<html>
<head>
  <base href="https://office.chaoxing.com/">
  <meta charset="UTF-8">
  <title>出错了</title>
<base href="https://office.chaoxing.com/">
<script type="text/javascript">
  var photoUrl = 'http://photo.chaoxing.com'
  var panUrl = 'http://pan-yz.chaoxing.com'
  var noteUrl = 'http://noteyd.chaoxing.com'
  var staticDomain = 'https://reserve.chaoxing.com/'
  photoUrl = document.location.protocol + photoUrl.split(":")[1]
  panUrl = document.location.protocol + panUrl.split(":")[1]
  noteUrl = document.location.protocol + noteUrl.split(":")[1]
  if (staticDomain !== '') {
    staticDomain = document.location.protocol + staticDomain.split(":")[1]
  }
</script>

<meta charset="UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
<meta name="format-detection" content="telephone=no,email=no,address=no">
<meta http-equiv="pragma" content="no-cache"/>
<meta http-equiv="cache-control" content
2025-06-05 21:16:45.351  WARN --- [pool-1-thread-1] c.s.service.XuexitongApiService : 所有token提取方式都失败
2025-06-05 21:16:45.351 ERROR --- [pool-1-thread-1] c.s.service.XuexitongApiService : 从页面HTML中未找到token
2025-06-05 21:16:45.353 ERROR --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取页面token失败，无法继续预约
2025-06-05 21:16:45.353  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约执行完成: success=false
2025-06-05 21:51:40.485  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始执行学习通预约: reservationId=46, userId=20, roomId=64, seatId=001
2025-06-05 21:51:40.487  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约时间详情: startTime=15:30, endTime=20:30, openTime=20:52:00, type=SAME_DAY
2025-06-05 21:51:40.487 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤1: 获取用户信息, userId=20
2025-06-05 21:51:40.493  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 用户信息获取成功: username=18755869972, name=预约陶承程, remainingDays=3
2025-06-05 21:51:40.495 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤2: 检查用户剩余天数, remainingDays=3
2025-06-05 21:51:40.496 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤3: 创建学习通会话
2025-06-05 21:51:40.498 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤4: 开始登录学习通, username=18755869972
2025-06-05 21:51:40.499  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始登录学习通: username=18755869972
2025-06-05 21:51:40.500 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤1: 获取登录页面，建立会话
2025-06-05 21:51:40.931 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 提取到 2 个Cookie
2025-06-05 21:51:40.932 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取登录页面成功
2025-06-05 21:51:40.932 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录页面获取完成，当前Cookie数量: 2
2025-06-05 21:51:40.932 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤2: 构建登录参数
2025-06-05 21:51:40.933 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录参数构建完成: fid=-1, refer=office.chaoxing.com
2025-06-05 21:51:40.934 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤3: 发送登录请求到 https://passport2.chaoxing.com/fanyalogin
2025-06-05 21:51:40.997 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录请求响应: status=200 OK, bodyLength=93
2025-06-05 21:51:40.997 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤4: 解析登录结果
2025-06-05 21:51:40.997 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录响应内容: {"url":"http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode","status":true}
2025-06-05 21:51:41.001 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 提取到 12 个Cookie
2025-06-05 21:51:41.001  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972, 总Cookie数量=12
2025-06-05 21:51:41.001  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972
2025-06-05 21:51:41.005 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤5: 获取实际的room_id
2025-06-05 21:51:41.013  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取到实际room_id: tableId=64, actualRoomId=10347
2025-06-05 21:51:41.014 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤6: 开始执行预约, actualRoomId=10347, seatId=001
2025-06-05 21:51:41.015  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始提交预约: roomId=10347, seatId=001, startTime=15:30, endTime=20:30
2025-06-05 21:51:41.015 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤1: 获取页面token
2025-06-05 21:51:41.016  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取页面token: url=https://office.chaoxing.com/front/third/apps/seat/code?id=10347&seatNum=001
2025-06-05 21:51:41.018 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 发送token页面请求，当前Cookie数量: 12
2025-06-05 21:51:42.060 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : token页面响应: status=200 OK, bodyLength=3803
2025-06-05 21:51:42.061 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取到的HTML内容前1000字符: <!DOCTYPE html>
<html>
<head>
  <base href="https://office.chaoxing.com/">
  <meta charset="UTF-8">
  <title>出错了</title>
<base href="https://office.chaoxing.com/">
<script type="text/javascript">
  var photoUrl = 'http://photo.chaoxing.com'
  var panUrl = 'http://pan-yz.chaoxing.com'
  var noteUrl = 'http://noteyd.chaoxing.com'
  var staticDomain = 'https://reserve.chaoxing.com/'
  photoUrl = document.location.protocol + photoUrl.split(":")[1]
  panUrl = document.location.protocol + panUrl.split(":")[1]
  noteUrl = document.location.protocol + noteUrl.split(":")[1]
  if (staticDomain !== '') {
    staticDomain = document.location.protocol + staticDomain.split(":")[1]
  }
</script>

<meta charset="UTF-8">
<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
<meta name="format-detection" content="telephone=no,email=no,address=no">
<meta http-equiv="pragma" content="no-cache"/>
<meta http-equiv="cache-control" content
2025-06-05 21:51:42.064  WARN --- [pool-1-thread-1] c.s.service.XuexitongApiService : 所有token提取方式都失败
2025-06-05 21:51:42.066 ERROR --- [pool-1-thread-1] c.s.service.XuexitongApiService : 从页面HTML中未找到token
2025-06-05 21:51:42.068  WARN --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取页面token失败，但继续尝试预约（用于调试POST响应）
2025-06-05 21:51:42.068  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 页面token获取成功: 
2025-06-05 21:51:42.069 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤2: 构建预约参数
2025-06-05 21:51:42.069 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 基础预约参数: {seatNum=001, captcha=, startTime=15:30, endTime=20:30, day=2025-06-05, roomId=10347, token=}
2025-06-05 21:51:42.069 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤3: 计算enc参数签名
2025-06-05 21:51:42.070 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : enc计算字符串: [captcha=][day=2025-06-05][endTime=20:30][roomId=10347][seatNum=001][startTime=15:30][token=][%sd`~7^/>N4!Q#){']
2025-06-05 21:51:42.072 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : enc签名计算完成: f1f16109814d225bcbd7ef336fbca73e
2025-06-05 21:51:42.072 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤4: 发送预约请求
2025-06-05 21:51:42.073  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === API请求头详情 ===
2025-06-05 21:51:42.075  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 21:51:42.076  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 21:51:42.076  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 21:51:42.077  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 21:51:42.077  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 21:51:42.077  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Host: [office.chaoxing.com]
2025-06-05 21:51:42.078  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Connection: [keep-alive]
2025-06-05 21:51:42.079  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Keep-Alive: [timeout=10]
2025-06-05 21:51:42.079  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === Cookie详情 ===
2025-06-05 21:51:42.080  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   fid: 2293
2025-06-05 21:51:42.080  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   xxtenc: f0119b62971e362736965edd39a422b6
2025-06-05 21:51:42.080  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   UID: 192319793
2025-06-05 21:51:42.081  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   uf: da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debf7dababef3d1583a913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c9bab36ea259817b32f0f2069d93a49d8f3d718d572fcfc7c
2025-06-05 21:51:42.081  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   route: 26e346b982eea47de2f6652532e77800
2025-06-05 21:51:42.082  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   _d: 1749131499552
2025-06-05 21:51:42.082  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   p_auth_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzE0OTk1NTQsImV4cCI6MTc0OTczNjI5OX0.VWxJ7yorxFJDEXPJ-0vUznkzns_cb5ZMBzb-qvnVnbI
2025-06-05 21:51:42.083  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   vc3: RJAvajmzUNdMD2uR%2FIGWb81bOUkN5o%2BBPOQQORptD4EIcFG30asc1409NommhPkB7Y0ZFBAEszRqE9Z8ML0AzStprZ62uGnNyhaRAIOiNdkwr%2B2ASeHpFIwQoQwppYQ3jly7M4KtoyYBk%2BuGDYkqT8pzAyJPR99xLgUXGY0LL7U%3D936032b3d750182a1e20928c9c7505b1
2025-06-05 21:51:42.083  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   cx_p_token: ec0dc05d9998bfecee377a5bf323fc42
2025-06-05 21:51:42.084  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   DSSTASH_LOG: C_38-UN_892-US_192319793-T_1749131499554
2025-06-05 21:51:42.084  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   JSESSIONID: 0D6826F1B3C71537FD36CB5509754937
2025-06-05 21:51:42.084  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   _uid: 192319793
2025-06-05 21:51:42.086  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : Cookie字符串: fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debf7dababef3d1583a913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c9bab36ea259817b32f0f2069d93a49d8f3d718d572fcfc7c; route=26e346b982eea47de2f6652532e77800; _d=1749131499552; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzE0OTk1NTQsImV4cCI6MTc0OTczNjI5OX0.VWxJ7yorxFJDEXPJ-0vUznkzns_cb5ZMBzb-qvnVnbI; vc3=RJAvajmzUNdMD2uR%2FIGWb81bOUkN5o%2BBPOQQORptD4EIcFG30asc1409NommhPkB7Y0ZFBAEszRqE9Z8ML0AzStprZ62uGnNyhaRAIOiNdkwr%2B2ASeHpFIwQoQwppYQ3jly7M4KtoyYBk%2BuGDYkqT8pzAyJPR99xLgUXGY0LL7U%3D936032b3d750182a1e20928c9c7505b1; cx_p_token=ec0dc05d9998bfecee377a5bf323fc42; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749131499554; JSESSIONID=0D6826F1B3C71537FD36CB5509754937; _uid=192319793
2025-06-05 21:51:42.087  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === API请求详情 ===
2025-06-05 21:51:42.088  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 请求方法: POST
2025-06-05 21:51:42.088  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 基础URL: https://office.chaoxing.com/data/apps/seat/submit
2025-06-05 21:51:42.088  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 请求参数详情: {seatNum=001, captcha=, startTime=15:30, endTime=20:30, enc=f1f16109814d225bcbd7ef336fbca73e, day=2025-06-05, roomId=10347, token=}
2025-06-05 21:51:42.089  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 参数字符串: seatNum=001&captcha=&startTime=15%3A30&endTime=20%3A30&enc=f1f16109814d225bcbd7ef336fbca73e&day=2025-06-05&roomId=10347&token=
2025-06-05 21:51:42.089  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 完整请求URL: https://office.chaoxing.com/data/apps/seat/submit?seatNum=001&captcha=&startTime=15%3A30&endTime=20%3A30&enc=f1f16109814d225bcbd7ef336fbca73e&day=2025-06-05&roomId=10347&token=
2025-06-05 21:51:42.090  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 请求体: 无 (使用URL参数)
2025-06-05 21:51:42.148  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === API响应详情 ===
2025-06-05 21:51:42.148  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应状态码: 200 OK
2025-06-05 21:51:42.149  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应头: [Date:"Thu, 05 Jun 2025 13:51:40 GMT", Content-Type:"text/html;charset=UTF-8", Content-Length:"87", Connection:"keep-alive", Set-Cookie:"JSESSIONID=9B453F8902E906485858CB8002881B24.reserve_web_126; Path=/; HttpOnly", "oa_deptid=2293; Path=/", "oa_uid=192319793; Path=/", "oa_name=%E9%99%B6%E6%89%BF%E7%A8%8B; Path=/", "oa_enc=c0cc4b805faf4b46c45887d757228349; Path=/", "oa_deptid=2293; Path=/", "oa_uid=192319793; Path=/", "oa_name=%E9%99%B6%E6%89%BF%E7%A8%8B; Path=/", "oa_enc=c0cc4b805faf4b46c45887d757228349; Path=/", "route=af71b26362ce81457b3e61b7e8b523ec; Path=/", Origin-Agent-Cluster:"?0"]
2025-06-05 21:51:42.150  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应体长度: 47
2025-06-05 21:51:42.151  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应体内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 21:51:42.151 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤5: 解析预约结果
2025-06-05 21:51:42.151 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约响应内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 21:51:42.152  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约提交结果: roomId=10347, seatId=001, success=false, message=当前使用人数较多，请5分钟后再次尝试提交！
2025-06-05 21:51:42.152 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 完整预约响应: {msg=当前使用人数较多，请5分钟后再次尝试提交！, success=false}
2025-06-05 21:51:42.153  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约执行完成: success=false
2025-06-05 22:14:09.481  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始执行学习通预约: reservationId=46, userId=20, roomId=64, seatId=005
2025-06-05 22:14:09.484  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约时间详情: startTime=15:30, endTime=20:30, openTime=20:52:00, type=SAME_DAY
2025-06-05 22:14:09.485 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤1: 获取用户信息, userId=20
2025-06-05 22:14:09.495  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 用户信息获取成功: username=18755869972, name=预约陶承程, remainingDays=3
2025-06-05 22:14:09.497 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤2: 检查用户剩余天数, remainingDays=3
2025-06-05 22:14:09.498 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤3: 创建学习通会话
2025-06-05 22:14:09.501 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤4: 开始登录学习通, username=18755869972
2025-06-05 22:14:09.502  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始登录学习通: username=18755869972
2025-06-05 22:14:09.503 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤1: 获取登录页面，建立会话
2025-06-05 22:14:09.899 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 提取到 2 个Cookie
2025-06-05 22:14:09.899 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取登录页面成功
2025-06-05 22:14:09.899 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录页面获取完成，当前Cookie数量: 2
2025-06-05 22:14:09.899 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤2: 构建登录参数
2025-06-05 22:14:09.901 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录参数构建完成: fid=-1, refer=office.chaoxing.com
2025-06-05 22:14:09.901 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤3: 发送登录请求到 https://passport2.chaoxing.com/fanyalogin
2025-06-05 22:14:09.960 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录请求响应: status=200 OK, bodyLength=93
2025-06-05 22:14:09.961 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录步骤4: 解析登录结果
2025-06-05 22:14:09.961 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 登录响应内容: {"url":"http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode","status":true}
2025-06-05 22:14:09.964 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 提取到 12 个Cookie
2025-06-05 22:14:09.965  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972, 总Cookie数量=12
2025-06-05 22:14:09.965  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972
2025-06-05 22:14:09.965 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤5: 获取实际的room_id
2025-06-05 22:14:09.968  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取到实际room_id: tableId=64, actualRoomId=4991
2025-06-05 22:14:09.969 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 步骤6: 开始执行预约, actualRoomId=4991, seatId=005
2025-06-05 22:14:09.969  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 开始提交预约: roomId=4991, seatId=005, startTime=15:30, endTime=20:30
2025-06-05 22:14:09.969 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤1: 获取页面token
2025-06-05 22:14:09.970  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取页面token: url=https://office.chaoxing.com/front/third/apps/seat/code?id=4991&seatNum=005
2025-06-05 22:14:09.970  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === Token获取请求头详情 ===
2025-06-05 22:14:09.971  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 22:14:09.971  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 22:14:09.971  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 22:14:09.972  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 22:14:09.972  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 22:14:09.974  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Cookie: [fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979deba92fd3f1241ed346913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78cd2e5cb580f32b137f777b34df938aaf7f3d718d572fcfc7c; route=fb0878d2b253f576b9614a77ccc901db; _d=1749132848488; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzI4NDg0ODksImV4cCI6MTc0OTczNzY0OH0.m2e52j75BPgF9HybrQiXiDgp1wXwtAcYHgww-fCxSes; vc3=E%2B75rtC15Mk%2FWMFkZTOWsYFjuJTCK%2BIxpe8pxmJ%2BCATcVacXkjN3cH7iHl9QWKQY3MXZlIMq4eTNIW%2FhCAnk1pQgAR7%2BYJXvGWepcz4b0nn5DCrQs6KZs42pri2UcqPs2viWHBzUnqiTl0zDhgEbmSTdd5VfT8MsTdw1b9iEUlA%3D6694525d349a9fc6150879aaff674e7a; cx_p_token=d244e8d6ca258b43a9a91560c0ef95d7; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749132848489; JSESSIONID=23FF7CA58249865BCBD5931D47CC1C61; _uid=192319793]
2025-06-05 22:14:09.975  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Referer: [https://office.chaoxing.com/]
2025-06-05 22:14:09.975 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 发送token页面请求，当前Cookie数量: 12
2025-06-05 22:14:10.691 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : token页面响应: status=200 OK, bodyLength=38818
2025-06-05 22:14:10.691 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 获取到的HTML内容前1000字符: <!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
  <meta name="format-detection" content="telephone=no">
  <title>座位预约</title>
<base href="https://office.chaoxing.com/">
<script type="text/javascript">
  var photoUrl = 'http://photo.chaoxing.com'
  var panUrl = 'http://pan-yz.chaoxing.com'
  var noteUrl = 'http://noteyd.chaoxing.com'
  var staticDomain = 'https://reserve.chaoxing.com/'
  photoUrl = document.location.protocol + photoUrl.split(":")[1]
  panUrl = document.location.protocol + panUrl.split(":")[1]
  noteUrl = document.location.protocol + noteUrl.split(":")[1]
  if (staticDomain !== '') {
    staticDomain = document.location.protocol + staticDomain.split(":")[1]
  }
</script>

<link rel="stylesheet" href="https://reserve.chaoxing.com/staticreserve/style/reset.css">
<link rel="stylesheet" href="https://reserve.chaoxing.com/staticreserve/style/apps/seat/seat.css">

2025-06-05 22:14:10.693 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 使用方式1提取token成功: 3c85a88b6d9b47aebffa641ec9452527
2025-06-05 22:14:10.693  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 页面token提取成功: 3c85a88b6d9b47aebffa641ec9452527
2025-06-05 22:14:10.693  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 页面token获取成功: 3c85a88b6d9b47aebffa641ec9452527
2025-06-05 22:14:10.694 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤2: 构建预约参数
2025-06-05 22:14:10.694 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 基础预约参数: {seatNum=005, captcha=, startTime=15:30, endTime=20:30, day=2025-06-05, roomId=4991, token=3c85a88b6d9b47aebffa641ec9452527}
2025-06-05 22:14:10.694 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤3: 计算enc参数签名
2025-06-05 22:14:10.694 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : enc计算字符串: [captcha=][day=2025-06-05][endTime=20:30][roomId=4991][seatNum=005][startTime=15:30][token=3c85a88b6d9b47aebffa641ec9452527][%sd`~7^/>N4!Q#){']
2025-06-05 22:14:10.694 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : enc签名计算完成: ad10e5e59c6f5758bfd7e9645d04a6ea
2025-06-05 22:14:10.694 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤4: 发送预约请求
2025-06-05 22:14:10.694  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === API请求头详情 ===
2025-06-05 22:14:10.694  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 22:14:10.697  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 22:14:10.697  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Host: [office.chaoxing.com]
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Connection: [keep-alive]
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   Keep-Alive: [timeout=10]
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === Cookie详情 ===
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   fid: 2293
2025-06-05 22:14:10.698  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   xxtenc: f0119b62971e362736965edd39a422b6
2025-06-05 22:14:10.699  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   UID: 192319793
2025-06-05 22:14:10.699  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   uf: da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979deba92fd3f1241ed346913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78cd2e5cb580f32b137f777b34df938aaf7f3d718d572fcfc7c
2025-06-05 22:14:10.699  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   route: fb0878d2b253f576b9614a77ccc901db
2025-06-05 22:14:10.699  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   _d: 1749132848488
2025-06-05 22:14:10.699  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   p_auth_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzI4NDg0ODksImV4cCI6MTc0OTczNzY0OH0.m2e52j75BPgF9HybrQiXiDgp1wXwtAcYHgww-fCxSes
2025-06-05 22:14:10.700  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   vc3: E%2B75rtC15Mk%2FWMFkZTOWsYFjuJTCK%2BIxpe8pxmJ%2BCATcVacXkjN3cH7iHl9QWKQY3MXZlIMq4eTNIW%2FhCAnk1pQgAR7%2BYJXvGWepcz4b0nn5DCrQs6KZs42pri2UcqPs2viWHBzUnqiTl0zDhgEbmSTdd5VfT8MsTdw1b9iEUlA%3D6694525d349a9fc6150879aaff674e7a
2025-06-05 22:14:10.700  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   cx_p_token: d244e8d6ca258b43a9a91560c0ef95d7
2025-06-05 22:14:10.700  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   DSSTASH_LOG: C_38-UN_892-US_192319793-T_1749132848489
2025-06-05 22:14:10.700  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   JSESSIONID: 23FF7CA58249865BCBD5931D47CC1C61
2025-06-05 22:14:10.701  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService :   _uid: 192319793
2025-06-05 22:14:10.702  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : Cookie字符串: fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979deba92fd3f1241ed346913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78cd2e5cb580f32b137f777b34df938aaf7f3d718d572fcfc7c; route=fb0878d2b253f576b9614a77ccc901db; _d=1749132848488; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzI4NDg0ODksImV4cCI6MTc0OTczNzY0OH0.m2e52j75BPgF9HybrQiXiDgp1wXwtAcYHgww-fCxSes; vc3=E%2B75rtC15Mk%2FWMFkZTOWsYFjuJTCK%2BIxpe8pxmJ%2BCATcVacXkjN3cH7iHl9QWKQY3MXZlIMq4eTNIW%2FhCAnk1pQgAR7%2BYJXvGWepcz4b0nn5DCrQs6KZs42pri2UcqPs2viWHBzUnqiTl0zDhgEbmSTdd5VfT8MsTdw1b9iEUlA%3D6694525d349a9fc6150879aaff674e7a; cx_p_token=d244e8d6ca258b43a9a91560c0ef95d7; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749132848489; JSESSIONID=23FF7CA58249865BCBD5931D47CC1C61; _uid=192319793
2025-06-05 22:14:10.706  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === API请求详情 ===
2025-06-05 22:14:10.707  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 请求方法: POST
2025-06-05 22:14:10.708  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 基础URL: https://office.chaoxing.com/data/apps/seat/submit
2025-06-05 22:14:10.708  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 请求参数详情: {seatNum=005, captcha=, startTime=15:30, endTime=20:30, enc=ad10e5e59c6f5758bfd7e9645d04a6ea, day=2025-06-05, roomId=4991, token=3c85a88b6d9b47aebffa641ec9452527}
2025-06-05 22:14:10.708  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 参数字符串: seatNum=005&captcha=&startTime=15%3A30&endTime=20%3A30&enc=ad10e5e59c6f5758bfd7e9645d04a6ea&day=2025-06-05&roomId=4991&token=3c85a88b6d9b47aebffa641ec9452527
2025-06-05 22:14:10.710  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 完整请求URL: https://office.chaoxing.com/data/apps/seat/submit?seatNum=005&captcha=&startTime=15%3A30&endTime=20%3A30&enc=ad10e5e59c6f5758bfd7e9645d04a6ea&day=2025-06-05&roomId=4991&token=3c85a88b6d9b47aebffa641ec9452527
2025-06-05 22:14:10.710  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 请求体: 无 (使用URL参数)
2025-06-05 22:14:10.774  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : === API响应详情 ===
2025-06-05 22:14:10.774  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应状态码: 200 OK
2025-06-05 22:14:10.774  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应头: [Date:"Thu, 05 Jun 2025 14:14:09 GMT", Content-Type:"text/html;charset=UTF-8", Content-Length:"87", Connection:"keep-alive", Set-Cookie:"JSESSIONID=F612ECA9FCF42E752C80D8A293BEC408.reserve_web_125; Path=/; HttpOnly", "oa_deptid=2293; Path=/", "oa_uid=192319793; Path=/", "oa_name=%E9%99%B6%E6%89%BF%E7%A8%8B; Path=/", "oa_enc=c0cc4b805faf4b46c45887d757228349; Path=/", "oa_deptid=2293; Path=/", "oa_uid=192319793; Path=/", "oa_name=%E9%99%B6%E6%89%BF%E7%A8%8B; Path=/", "oa_enc=c0cc4b805faf4b46c45887d757228349; Path=/", "route=2107b3b209f27d6acae2395f23ff4722; Path=/", Origin-Agent-Cluster:"?0"]
2025-06-05 22:14:10.778  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应体长度: 47
2025-06-05 22:14:10.779  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 响应体内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 22:14:10.780 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约步骤5: 解析预约结果
2025-06-05 22:14:10.780 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约响应内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 22:14:10.780  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约提交结果: roomId=4991, seatId=005, success=false, message=当前使用人数较多，请5分钟后再次尝试提交！
2025-06-05 22:14:10.781 DEBUG --- [pool-1-thread-1] c.s.service.XuexitongApiService : 完整预约响应: {msg=当前使用人数较多，请5分钟后再次尝试提交！, success=false}
2025-06-05 22:14:10.781  INFO --- [pool-1-thread-1] c.s.service.XuexitongApiService : 预约执行完成: success=false
2025-06-05 22:21:20.276  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 开始执行学习通预约: reservationId=46, userId=20, roomId=64, seatId=005
2025-06-05 22:21:20.277  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约时间详情: startTime=15:30, endTime=20:30, openTime=20:52:00, type=SAME_DAY
2025-06-05 22:21:20.277 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 步骤1: 获取用户信息, userId=20
2025-06-05 22:21:20.280  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 用户信息获取成功: username=18755869972, name=预约陶承程, remainingDays=3
2025-06-05 22:21:20.280 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 步骤2: 检查用户剩余天数, remainingDays=3
2025-06-05 22:21:20.281 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 步骤3: 创建学习通会话
2025-06-05 22:21:20.281 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 步骤4: 开始登录学习通, username=18755869972
2025-06-05 22:21:20.281  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 开始登录学习通: username=18755869972
2025-06-05 22:21:20.281 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录步骤1: 获取登录页面，建立会话
2025-06-05 22:21:20.395 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 获取登录页面成功
2025-06-05 22:21:20.395 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录页面获取完成，当前Cookie数量: 0
2025-06-05 22:21:20.395 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录步骤2: 构建登录参数
2025-06-05 22:21:20.395 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录参数构建完成: fid=-1, refer=office.chaoxing.com
2025-06-05 22:21:20.395 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录步骤3: 发送登录请求到 https://passport2.chaoxing.com/fanyalogin
2025-06-05 22:21:20.446 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录请求响应: status=200 OK, bodyLength=93
2025-06-05 22:21:20.446 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录步骤4: 解析登录结果
2025-06-05 22:21:20.446 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 登录响应内容: {"url":"http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode","status":true}
2025-06-05 22:21:20.447 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 提取到 10 个Cookie
2025-06-05 22:21:20.447  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972, 总Cookie数量=10
2025-06-05 22:21:20.447  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972
2025-06-05 22:21:20.447 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 步骤5: 获取实际的room_id
2025-06-05 22:21:20.450  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 获取到实际room_id: tableId=64, actualRoomId=4991
2025-06-05 22:21:20.450 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 步骤6: 开始执行预约, actualRoomId=4991, seatId=005
2025-06-05 22:21:20.451  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 开始提交预约: roomId=4991, seatId=005, startTime=15:30, endTime=20:30
2025-06-05 22:21:20.451 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约步骤1: 获取页面token
2025-06-05 22:21:20.451  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 获取页面token: url=https://office.chaoxing.com/front/third/apps/seat/code?id=4991&seatNum=005
2025-06-05 22:21:20.452  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : === Token获取请求头详情 ===
2025-06-05 22:21:20.452  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 22:21:20.452  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 22:21:20.453  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 22:21:20.453  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 22:21:20.453  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 22:21:20.453  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Cookie: [fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debb975b73b9a710d1c913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c1fa9ac7336ddde404788fdb0a64982fdf3d718d572fcfc7c; _d=1749133278976; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzMyNzg5NzgsImV4cCI6MTc0OTczODA3OH0.tU7p6zqjKRWfs6-M17mqh5FGlOtHFcYmYSTq0YNV2pI; vc3=DfMifKUmHv8ir3LAY6189dJTQEJ2qoyJ39r2nih8J%2F9vIFLoY9Il%2BjTzvHWQ2tBenGtVKB5Qtcv9BcbsTIZAtxOfch1otFLOWBb1BSXlH%2BnMnpyTCnSRgB4cFt6GsnkpAQP6VQx3nlqiEm8wxdEw%2BAP6FI%2BPqVtv%2BkoWPP3srFA%3Df52d9048c40c18474cc9c5f337620030; cx_p_token=f416c071216abb061705ec133b07bb84; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749133278978; _uid=192319793]
2025-06-05 22:21:20.454  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Referer: [https://office.chaoxing.com/]
2025-06-05 22:21:20.454 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 发送token页面请求，当前Cookie数量: 10
2025-06-05 22:21:21.206 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : token页面响应: status=200 OK, bodyLength=38818
2025-06-05 22:21:21.206 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 获取到的HTML内容前1000字符: <!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
  <meta name="format-detection" content="telephone=no">
  <title>座位预约</title>
<base href="https://office.chaoxing.com/">
<script type="text/javascript">
  var photoUrl = 'http://photo.chaoxing.com'
  var panUrl = 'http://pan-yz.chaoxing.com'
  var noteUrl = 'http://noteyd.chaoxing.com'
  var staticDomain = 'https://reserve.chaoxing.com/'
  photoUrl = document.location.protocol + photoUrl.split(":")[1]
  panUrl = document.location.protocol + panUrl.split(":")[1]
  noteUrl = document.location.protocol + noteUrl.split(":")[1]
  if (staticDomain !== '') {
    staticDomain = document.location.protocol + staticDomain.split(":")[1]
  }
</script>

<link rel="stylesheet" href="https://reserve.chaoxing.com/staticreserve/style/reset.css">
<link rel="stylesheet" href="https://reserve.chaoxing.com/staticreserve/style/apps/seat/seat.css">

2025-06-05 22:21:21.206 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 使用方式1提取token成功: 97e7bdf2004e4a7da93967d8e4fe93fe
2025-06-05 22:21:21.206  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 页面token提取成功: 97e7bdf2004e4a7da93967d8e4fe93fe
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 页面token获取成功: 97e7bdf2004e4a7da93967d8e4fe93fe
2025-06-05 22:21:21.210 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约步骤2: 构建预约参数
2025-06-05 22:21:21.210 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 基础预约参数: {seatNum=005, captcha=, startTime=15:30, endTime=20:30, day=2025-06-05, roomId=4991, token=97e7bdf2004e4a7da93967d8e4fe93fe}
2025-06-05 22:21:21.210 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约步骤3: 计算enc参数签名
2025-06-05 22:21:21.210 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : enc计算字符串: [captcha=][day=2025-06-05][endTime=20:30][roomId=4991][seatNum=005][startTime=15:30][token=97e7bdf2004e4a7da93967d8e4fe93fe][%sd`~7^/>N4!Q#){']
2025-06-05 22:21:21.210 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : enc签名计算完成: 9ca90bcaa3425a8af355a9f05742bf25
2025-06-05 22:21:21.210 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约步骤4: 发送预约请求
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : === API请求头详情 ===
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Host: [office.chaoxing.com]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Connection: [keep-alive]
2025-06-05 22:21:21.210  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   Keep-Alive: [timeout=10]
2025-06-05 22:21:21.214  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : === Cookie详情 ===
2025-06-05 22:21:21.214  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   fid: 2293
2025-06-05 22:21:21.214  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   xxtenc: f0119b62971e362736965edd39a422b6
2025-06-05 22:21:21.214  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   UID: 192319793
2025-06-05 22:21:21.214  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   uf: da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debb975b73b9a710d1c913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c1fa9ac7336ddde404788fdb0a64982fdf3d718d572fcfc7c
2025-06-05 22:21:21.216  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   _d: 1749133278976
2025-06-05 22:21:21.216  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   p_auth_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzMyNzg5NzgsImV4cCI6MTc0OTczODA3OH0.tU7p6zqjKRWfs6-M17mqh5FGlOtHFcYmYSTq0YNV2pI
2025-06-05 22:21:21.216  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   vc3: DfMifKUmHv8ir3LAY6189dJTQEJ2qoyJ39r2nih8J%2F9vIFLoY9Il%2BjTzvHWQ2tBenGtVKB5Qtcv9BcbsTIZAtxOfch1otFLOWBb1BSXlH%2BnMnpyTCnSRgB4cFt6GsnkpAQP6VQx3nlqiEm8wxdEw%2BAP6FI%2BPqVtv%2BkoWPP3srFA%3Df52d9048c40c18474cc9c5f337620030
2025-06-05 22:21:21.216  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   cx_p_token: f416c071216abb061705ec133b07bb84
2025-06-05 22:21:21.216  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   DSSTASH_LOG: C_38-UN_892-US_192319793-T_1749133278978
2025-06-05 22:21:21.216  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService :   _uid: 192319793
2025-06-05 22:21:21.217  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : Cookie字符串: fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debb975b73b9a710d1c913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c1fa9ac7336ddde404788fdb0a64982fdf3d718d572fcfc7c; _d=1749133278976; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzMyNzg5NzgsImV4cCI6MTc0OTczODA3OH0.tU7p6zqjKRWfs6-M17mqh5FGlOtHFcYmYSTq0YNV2pI; vc3=DfMifKUmHv8ir3LAY6189dJTQEJ2qoyJ39r2nih8J%2F9vIFLoY9Il%2BjTzvHWQ2tBenGtVKB5Qtcv9BcbsTIZAtxOfch1otFLOWBb1BSXlH%2BnMnpyTCnSRgB4cFt6GsnkpAQP6VQx3nlqiEm8wxdEw%2BAP6FI%2BPqVtv%2BkoWPP3srFA%3Df52d9048c40c18474cc9c5f337620030; cx_p_token=f416c071216abb061705ec133b07bb84; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749133278978; _uid=192319793
2025-06-05 22:21:21.217  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : === API请求详情 ===
2025-06-05 22:21:21.217  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 请求方法: POST
2025-06-05 22:21:21.217  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 基础URL: https://office.chaoxing.com/data/apps/seat/submit
2025-06-05 22:21:21.217  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 请求参数详情: {seatNum=005, captcha=, startTime=15:30, endTime=20:30, enc=9ca90bcaa3425a8af355a9f05742bf25, day=2025-06-05, roomId=4991, token=97e7bdf2004e4a7da93967d8e4fe93fe}
2025-06-05 22:21:21.218  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 参数字符串: seatNum=005&captcha=&startTime=15%3A30&endTime=20%3A30&enc=9ca90bcaa3425a8af355a9f05742bf25&day=2025-06-05&roomId=4991&token=97e7bdf2004e4a7da93967d8e4fe93fe
2025-06-05 22:21:21.218  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 完整请求URL: https://office.chaoxing.com/data/apps/seat/submit?seatNum=005&captcha=&startTime=15%3A30&endTime=20%3A30&enc=9ca90bcaa3425a8af355a9f05742bf25&day=2025-06-05&roomId=4991&token=97e7bdf2004e4a7da93967d8e4fe93fe
2025-06-05 22:21:21.219  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 请求体: 无 (使用URL参数)
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : === API响应详情 ===
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 响应状态码: 200 OK
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 响应头: [Date:"Thu, 05 Jun 2025 14:21:19 GMT", Content-Type:"text/html;charset=UTF-8", Content-Length:"87", Connection:"keep-alive", Origin-Agent-Cluster:"?0"]
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 响应体长度: 47
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 响应体内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 22:21:21.252 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约步骤5: 解析预约结果
2025-06-05 22:21:21.252 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约响应内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约提交结果: roomId=4991, seatId=005, success=false, message=当前使用人数较多，请5分钟后再次尝试提交！
2025-06-05 22:21:21.252 DEBUG --- [pool-1-thread-2] c.s.service.XuexitongApiService : 完整预约响应: {msg=当前使用人数较多，请5分钟后再次尝试提交！, success=false}
2025-06-05 22:21:21.252  INFO --- [pool-1-thread-2] c.s.service.XuexitongApiService : 预约执行完成: success=false
2025-06-05 22:23:15.272  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 开始执行学习通预约: reservationId=46, userId=20, roomId=64, seatId=005
2025-06-05 22:23:15.273  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约时间详情: startTime=15:30, endTime=20:30, openTime=20:52:00, type=SAME_DAY
2025-06-05 22:23:15.273 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 步骤1: 获取用户信息, userId=20
2025-06-05 22:23:15.276  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 用户信息获取成功: username=18755869972, name=预约陶承程, remainingDays=3
2025-06-05 22:23:15.277 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 步骤2: 检查用户剩余天数, remainingDays=3
2025-06-05 22:23:15.277 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 步骤3: 创建学习通会话
2025-06-05 22:23:15.278 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 步骤4: 开始登录学习通, username=18755869972
2025-06-05 22:23:15.278  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 开始登录学习通: username=18755869972
2025-06-05 22:23:15.278 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录步骤1: 获取登录页面，建立会话
2025-06-05 22:23:15.405 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 获取登录页面成功
2025-06-05 22:23:15.405 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录页面获取完成，当前Cookie数量: 0
2025-06-05 22:23:15.405 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录步骤2: 构建登录参数
2025-06-05 22:23:15.405 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录参数构建完成: fid=-1, refer=office.chaoxing.com
2025-06-05 22:23:15.405 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录步骤3: 发送登录请求到 https://passport2.chaoxing.com/fanyalogin
2025-06-05 22:23:15.467 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录请求响应: status=200 OK, bodyLength=93
2025-06-05 22:23:15.467 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录步骤4: 解析登录结果
2025-06-05 22:23:15.467 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 登录响应内容: {"url":"http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode","status":true}
2025-06-05 22:23:15.468 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 提取到 10 个Cookie
2025-06-05 22:23:15.468  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972, 总Cookie数量=10
2025-06-05 22:23:15.468  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972
2025-06-05 22:23:15.468 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 步骤5: 获取实际的room_id
2025-06-05 22:23:15.472  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 获取到实际room_id: tableId=64, actualRoomId=4991
2025-06-05 22:23:15.472 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 步骤6: 开始执行预约, actualRoomId=4991, seatId=005
2025-06-05 22:23:15.472  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 开始提交预约: roomId=4991, seatId=005, startTime=15:30, endTime=20:30
2025-06-05 22:23:15.473 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约步骤1: 获取页面token
2025-06-05 22:23:15.473  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 获取页面token: url=https://office.chaoxing.com/front/third/apps/seat/code?id=4991&seatNum=005
2025-06-05 22:23:15.473  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : === Token获取请求头详情 ===
2025-06-05 22:23:15.473  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 22:23:15.474  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 22:23:15.474  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 22:23:15.474  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 22:23:15.474  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 22:23:15.474  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Cookie: [fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debb806b28128891f69913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c1fa9ac7336ddde408ef70a2205f43cf2f3d718d572fcfc7c; _d=1749133393995; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzMzOTM5OTcsImV4cCI6MTc0OTczODE5M30.6BNMzOL5acXEY1-s5gkT5OTRQ_ixn-FQt8fpLYKEenw; vc3=TBix8owrBN6ffQXohL97GGgXZP0pkGxR%2B43wMyZE13gAulvTBWDX2%2Bh3HlUuCXAt0iz5t2z%2BwmhKX%2FC8uNxRO7GpHyv%2BmJbufamtSsOPqu7vQbRtl4Kh8v1TZc%2Bi7MXLB04mywWk1rkdi2YKSNFAKcnNVY10B9a%2BxGZDtbXj%2BcY%3Da6484287b10bebe0407c6a4121bdf238; cx_p_token=329760003a76bd417d01358cf9cb190e; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749133393997; _uid=192319793]
2025-06-05 22:23:15.475  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Referer: [https://office.chaoxing.com/]
2025-06-05 22:23:15.475 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 发送token页面请求，当前Cookie数量: 10
2025-06-05 22:23:16.206 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : token页面响应: status=200 OK, bodyLength=38818
2025-06-05 22:23:16.206 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 获取到的HTML内容前1000字符: <!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
  <meta name="format-detection" content="telephone=no">
  <title>座位预约</title>
<base href="https://office.chaoxing.com/">
<script type="text/javascript">
  var photoUrl = 'http://photo.chaoxing.com'
  var panUrl = 'http://pan-yz.chaoxing.com'
  var noteUrl = 'http://noteyd.chaoxing.com'
  var staticDomain = 'https://reserve.chaoxing.com/'
  photoUrl = document.location.protocol + photoUrl.split(":")[1]
  panUrl = document.location.protocol + panUrl.split(":")[1]
  noteUrl = document.location.protocol + noteUrl.split(":")[1]
  if (staticDomain !== '') {
    staticDomain = document.location.protocol + staticDomain.split(":")[1]
  }
</script>

<link rel="stylesheet" href="https://reserve.chaoxing.com/staticreserve/style/reset.css">
<link rel="stylesheet" href="https://reserve.chaoxing.com/staticreserve/style/apps/seat/seat.css">

2025-06-05 22:23:16.207 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 使用方式1提取token成功: f9f9dc66d24a48c68c8cc48c570bf773
2025-06-05 22:23:16.207  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 页面token提取成功: f9f9dc66d24a48c68c8cc48c570bf773
2025-06-05 22:23:16.207  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 页面token获取成功: f9f9dc66d24a48c68c8cc48c570bf773
2025-06-05 22:23:16.207 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约步骤2: 构建预约参数
2025-06-05 22:23:16.207 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 基础预约参数: {seatNum=005, captcha=, startTime=15:30, endTime=20:30, day=2025-06-05, roomId=4991, token=f9f9dc66d24a48c68c8cc48c570bf773}
2025-06-05 22:23:16.208 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约步骤3: 计算enc参数签名
2025-06-05 22:23:16.208 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : enc计算字符串: [captcha=][day=2025-06-05][endTime=20:30][roomId=4991][seatNum=005][startTime=15:30][token=f9f9dc66d24a48c68c8cc48c570bf773][%sd`~7^/>N4!Q#){']
2025-06-05 22:23:16.208 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : enc签名计算完成: 5a31ebe0440efef42564450a7671bca0
2025-06-05 22:23:16.208 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约步骤4: 发送预约请求
2025-06-05 22:23:16.209  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : === API请求头详情 ===
2025-06-05 22:23:16.209  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Accept: [text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3]
2025-06-05 22:23:16.209  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Accept-Language: [zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7]
2025-06-05 22:23:16.209  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   User-Agent: [Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638]
2025-06-05 22:23:16.209  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   X-Requested-With: [XMLHttpRequest]
2025-06-05 22:23:16.209  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Content-Type: [application/x-www-form-urlencoded; charset=UTF-8]
2025-06-05 22:23:16.210  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Host: [office.chaoxing.com]
2025-06-05 22:23:16.210  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Connection: [keep-alive]
2025-06-05 22:23:16.210  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   Keep-Alive: [timeout=10]
2025-06-05 22:23:16.210  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : === Cookie详情 ===
2025-06-05 22:23:16.210  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   fid: 2293
2025-06-05 22:23:16.211  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   xxtenc: f0119b62971e362736965edd39a422b6
2025-06-05 22:23:16.211  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   UID: 192319793
2025-06-05 22:23:16.211  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   uf: da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debb806b28128891f69913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c1fa9ac7336ddde408ef70a2205f43cf2f3d718d572fcfc7c
2025-06-05 22:23:16.211  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   _d: 1749133393995
2025-06-05 22:23:16.211  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   p_auth_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzMzOTM5OTcsImV4cCI6MTc0OTczODE5M30.6BNMzOL5acXEY1-s5gkT5OTRQ_ixn-FQt8fpLYKEenw
2025-06-05 22:23:16.212  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   vc3: TBix8owrBN6ffQXohL97GGgXZP0pkGxR%2B43wMyZE13gAulvTBWDX2%2Bh3HlUuCXAt0iz5t2z%2BwmhKX%2FC8uNxRO7GpHyv%2BmJbufamtSsOPqu7vQbRtl4Kh8v1TZc%2Bi7MXLB04mywWk1rkdi2YKSNFAKcnNVY10B9a%2BxGZDtbXj%2BcY%3Da6484287b10bebe0407c6a4121bdf238
2025-06-05 22:23:16.212  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   cx_p_token: 329760003a76bd417d01358cf9cb190e
2025-06-05 22:23:16.212  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   DSSTASH_LOG: C_38-UN_892-US_192319793-T_1749133393997
2025-06-05 22:23:16.212  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService :   _uid: 192319793
2025-06-05 22:23:16.212  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : Cookie字符串: fid=2293; xxtenc=f0119b62971e362736965edd39a422b6; UID=192319793; uf=da0883eb5260151efb7ac9722682fc7a4d3092f55988687e6fe9e5e7b93eaefa6f223d7b3c979debb806b28128891f69913b662843f1f4ade9295d8c89b08ad0f44425e20f927c6b8d6b40a696275c8cb919cf4f30ba0bf0c589688fe9065f49fd68be96b6183b1ad8eccfb0b782d78c1fa9ac7336ddde408ef70a2205f43cf2f3d718d572fcfc7c; _d=1749133393995; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIxOTIzMTk3OTMiLCJsb2dpblRpbWUiOjE3NDkxMzMzOTM5OTcsImV4cCI6MTc0OTczODE5M30.6BNMzOL5acXEY1-s5gkT5OTRQ_ixn-FQt8fpLYKEenw; vc3=TBix8owrBN6ffQXohL97GGgXZP0pkGxR%2B43wMyZE13gAulvTBWDX2%2Bh3HlUuCXAt0iz5t2z%2BwmhKX%2FC8uNxRO7GpHyv%2BmJbufamtSsOPqu7vQbRtl4Kh8v1TZc%2Bi7MXLB04mywWk1rkdi2YKSNFAKcnNVY10B9a%2BxGZDtbXj%2BcY%3Da6484287b10bebe0407c6a4121bdf238; cx_p_token=329760003a76bd417d01358cf9cb190e; DSSTASH_LOG=C_38-UN_892-US_192319793-T_1749133393997; _uid=192319793
2025-06-05 22:23:16.213  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : === API请求详情 ===
2025-06-05 22:23:16.213  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 请求方法: POST
2025-06-05 22:23:16.213  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 基础URL: https://office.chaoxing.com/data/apps/seat/submit
2025-06-05 22:23:16.213  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 请求参数详情: {seatNum=005, captcha=, startTime=15:30, endTime=20:30, enc=5a31ebe0440efef42564450a7671bca0, day=2025-06-05, roomId=4991, token=f9f9dc66d24a48c68c8cc48c570bf773}
2025-06-05 22:23:16.214  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 参数字符串: seatNum=005&captcha=&startTime=15%3A30&endTime=20%3A30&enc=5a31ebe0440efef42564450a7671bca0&day=2025-06-05&roomId=4991&token=f9f9dc66d24a48c68c8cc48c570bf773
2025-06-05 22:23:16.214  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 完整请求URL: https://office.chaoxing.com/data/apps/seat/submit?seatNum=005&captcha=&startTime=15%3A30&endTime=20%3A30&enc=5a31ebe0440efef42564450a7671bca0&day=2025-06-05&roomId=4991&token=f9f9dc66d24a48c68c8cc48c570bf773
2025-06-05 22:23:16.214  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 请求体: 无 (使用URL参数)
2025-06-05 22:23:16.246  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : === API响应详情 ===
2025-06-05 22:23:16.246  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 响应状态码: 200 OK
2025-06-05 22:23:16.247  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 响应头: [Date:"Thu, 05 Jun 2025 14:23:14 GMT", Content-Type:"text/html;charset=UTF-8", Content-Length:"87", Connection:"keep-alive", Origin-Agent-Cluster:"?0"]
2025-06-05 22:23:16.247  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 响应体长度: 47
2025-06-05 22:23:16.247  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 响应体内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 22:23:16.247 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约步骤5: 解析预约结果
2025-06-05 22:23:16.247 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约响应内容: {"msg":"当前使用人数较多，请5分钟后再次尝试提交！","success":false}
2025-06-05 22:23:16.248  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约提交结果: roomId=4991, seatId=005, success=false, message=当前使用人数较多，请5分钟后再次尝试提交！
2025-06-05 22:23:16.248 DEBUG --- [pool-1-thread-3] c.s.service.XuexitongApiService : 完整预约响应: {msg=当前使用人数较多，请5分钟后再次尝试提交！, success=false}
2025-06-05 22:23:16.248  INFO --- [pool-1-thread-3] c.s.service.XuexitongApiService : 预约执行完成: success=false
