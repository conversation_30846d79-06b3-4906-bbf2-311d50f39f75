package com.seatmaster.service;

import com.seatmaster.dto.ExecutionResult;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.User;
import com.seatmaster.entity.Room;
import com.seatmaster.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.stream.Collectors;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 学习通API服务
 * 负责与学习通系统进行交互，执行自动预约
 */
@Service
@Slf4j
public class XuexitongApiService {
    
    @Autowired
    private UserService userService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RoomService roomService;
    
    @Value("${xuexitong.base-url:https://passport2.chaoxing.com}")
    private String baseUrl;
    
    @Value("${xuexitong.office-url:https://office.chaoxing.com}")
    private String officeUrl;
    
    @Value("${xuexitong.timeout:30000}")
    private int timeout;
    
    // API路径常量
    private static final String LOGIN_URL = "/fanyalogin";
    private static final String SUBMIT_URL = "/data/apps/seat/submit";
    private static final String SEAT_URL = "/data/apps/seat/getusedtimes";
    
    // AES加密相关常量 (从Python脚本中提取)
    private static final String AES_KEY = "u2oh6Vu^HWe4_AES";
    private static final String AES_IV = "u2oh6Vu^HWe4_AES";
    
    /**
     * 执行学习通预约 - 主入口方法
     * 
     * @param reservation 预约信息
     * @return 执行结果
     */
    public ExecutionResult executeReservation(Reservation reservation) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始执行学习通预约: reservationId={}, userId={}, roomId={}, seatId={}",
                reservation.getId(), reservation.getUserId(), reservation.getRoomId(), reservation.getSeatId());
            log.info("预约时间详情: startTime={}, endTime={}, openTime={}, type={}",
                reservation.getStartTime(), reservation.getEndTime(),
                reservation.getReservationOpenTime(), reservation.getReservationType());

            // 1. 获取用户信息
            log.debug("步骤1: 获取用户信息, userId={}", reservation.getUserId());
            User user = userService.getById(reservation.getUserId());
            if (user == null) {
                log.error("用户不存在: userId={}", reservation.getUserId());
                return ExecutionResult.failure("用户不存在", "USER_NOT_FOUND");
            }
            log.info("用户信息获取成功: username={}, name={}, remainingDays={}",
                user.getUsername(), user.getName(), user.getRemainingDays());

            // 2. 检查用户剩余天数
            log.debug("步骤2: 检查用户剩余天数, remainingDays={}", user.getRemainingDays());
            if (user.getRemainingDays() <= 0) {
                log.warn("用户剩余天数不足: userId={}, remainingDays={}",
                    reservation.getUserId(), user.getRemainingDays());
                return ExecutionResult.failure("用户剩余天数不足", "INSUFFICIENT_DAYS");
            }
            
            // 3. 创建学习通会话
            log.debug("步骤3: 创建学习通会话");
            XuexitongSession session = new XuexitongSession();

            // 4. 登录学习通
            log.debug("步骤4: 开始登录学习通, username={}", user.getUsername());
            if (!session.login(user.getUsername(), user.getPassword())) {
                log.error("学习通登录失败: username={}", user.getUsername());
                return ExecutionResult.failure("学习通登录失败", "LOGIN_FAILED");
            }
            log.info("学习通登录成功: username={}", user.getUsername());

            // 5. 获取实际的room_id
            log.debug("步骤5: 获取实际的room_id");
            String actualRoomId = getRoomIdFromDatabase(reservation.getRoomId());
            if (actualRoomId == null) {
                log.error("无法获取房间的实际room_id: roomTableId={}", reservation.getRoomId());
                return ExecutionResult.failure("房间信息错误", "ROOM_NOT_FOUND");
            }
            log.info("获取到实际room_id: tableId={}, actualRoomId={}", reservation.getRoomId(), actualRoomId);

            // 6. 执行预约
            log.debug("步骤6: 开始执行预约, actualRoomId={}, seatId={}",
                actualRoomId, reservation.getSeatId());
            boolean success = session.submitReservation(
                actualRoomId,
                reservation.getSeatId(),
                reservation.getStartTime().toString(),
                reservation.getEndTime().toString()
            );
            log.info("预约执行完成: success={}", success);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (success) {
                log.info("学习通预约成功: userId={}, roomId={}, seatId={}, duration={}ms", 
                    user.getId(), reservation.getRoomId(), reservation.getSeatId(), duration);
                
                return ExecutionResult.success(
                    "预约成功", 
                    String.format("成功预约房间%d座位%s，耗时%dms", 
                        reservation.getRoomId(), reservation.getSeatId(), duration)
                ).withDuration(duration);
            } else {
                return ExecutionResult.failure("座位预约失败", "RESERVATION_FAILED")
                    .withDuration(duration);
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("学习通预约异常: userId={}, error={}, duration={}ms", 
                reservation.getUserId(), e.getMessage(), duration, e);
            
            return ExecutionResult.failure("预约异常: " + e.getMessage(), "SYSTEM_ERROR")
                .withDuration(duration);
        }
    }
    
    /**
     * 学习通会话管理内部类
     * 负责维护与学习通的HTTP会话
     */
    private class XuexitongSession {
        private final Map<String, String> cookies = new HashMap<>();
        private String token;
        
        /**
         * 登录学习通
         * 
         * @param username 用户名
         * @param password 密码
         * @return 登录是否成功
         */
        public boolean login(String username, String password) {
            try {
                log.info("开始登录学习通: username={}", username);

                // 1. 获取登录页面，建立会话
                log.debug("登录步骤1: 获取登录页面，建立会话");
                getLoginPage();
                log.debug("登录页面获取完成，当前Cookie数量: {}", cookies.size());

                // 2. 构建登录参数
                log.debug("登录步骤2: 构建登录参数");
                MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
                params.add("fid", "-1");
                params.add("uname", aesEncrypt(username));
                params.add("password", aesEncrypt(password));
                params.add("refer", "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode");
                params.add("t", "true");
                log.debug("登录参数构建完成: fid=-1, refer=office.chaoxing.com");
                
                // 3. 发送登录请求
                log.debug("登录步骤3: 发送登录请求到 {}", baseUrl + LOGIN_URL);
                HttpHeaders headers = createLoginHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

                ResponseEntity<String> response = restTemplate.postForEntity(
                    baseUrl + LOGIN_URL, request, String.class);

                log.debug("登录请求响应: status={}, bodyLength={}",
                    response.getStatusCode(),
                    response.getBody() != null ? response.getBody().length() : 0);

                // 4. 解析登录结果
                log.debug("登录步骤4: 解析登录结果");
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    log.debug("登录响应内容: {}", responseBody);

                    Map<String, Object> result = JsonUtils.fromJsonToMap(responseBody);

                    if (result != null && Boolean.TRUE.equals(result.get("status"))) {
                        // 提取Cookie
                        extractCookies(response);
                        log.info("学习通登录成功: username={}, 总Cookie数量={}", username, cookies.size());
                        return true;
                    } else {
                        String errorMsg = result != null ? (String) result.get("msg2") : "未知错误";
                        log.error("学习通登录失败: username={}, message={}, 完整响应={}",
                            username, errorMsg, result);
                    }
                } else {
                    log.error("登录请求失败: status={}, body={}",
                        response.getStatusCode(), response.getBody());
                }
                
                return false;
                
            } catch (Exception e) {
                log.error("学习通登录异常: username={}, error={}", username, e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 提交预约
         *
         * @param roomId 房间ID (实际的room_id字符串)
         * @param seatId 座位ID
         * @param startTime 开始时间
         * @param endTime 结束时间
         * @return 预约是否成功
         */
        public boolean submitReservation(String roomId, String seatId, String startTime, String endTime) {
            try {
                log.info("开始提交预约: roomId={}, seatId={}, startTime={}, endTime={}",
                    roomId, seatId, startTime, endTime);

                // 1. 获取页面token
                log.debug("预约步骤1: 获取页面token");
                String pageToken = getPageToken(roomId, seatId);
                if (pageToken == null) {
                    log.warn("获取页面token失败，但继续尝试预约（用于调试POST响应）");
                    pageToken = ""; // 使用空token继续测试
                }
                log.info("页面token获取成功: {}", pageToken);
                
                // 2. 构建预约参数 - 按照Python版本的参数格式
                log.debug("预约步骤2: 构建预约参数");

                // 计算预约日期 (今天)
                String day = LocalDate.now().toString();

                Map<String, String> submitParams = new HashMap<>();
                submitParams.put("roomId", roomId);  // 使用实际的room_id
                submitParams.put("seatNum", seatId); // Python版本使用seatNum而不是seatId
                submitParams.put("startTime", startTime);
                submitParams.put("endTime", endTime);
                submitParams.put("day", day);        // 添加day参数
                submitParams.put("token", pageToken);
                submitParams.put("captcha", "");     // 暂不处理验证码
                log.debug("基础预约参数: {}", submitParams);

                // 3. 计算enc参数 (参数签名)
                log.debug("预约步骤3: 计算enc参数签名");
                String enc = calculateEnc(submitParams);
                submitParams.put("enc", enc);
                log.debug("enc签名计算完成: {}", enc);
                
                // 4. 发送预约请求 - 完全按照Python脚本的方式
                log.debug("预约步骤4: 发送预约请求");
                String submitUrl = officeUrl + SUBMIT_URL;

                // 按照Python脚本的login_headers设置请求头（不包含Referer）
                HttpHeaders headers = new HttpHeaders();
                headers.add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3");
                headers.add("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
                headers.add("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638");
                headers.add("X-Requested-With", "XMLHttpRequest");
                headers.add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
                headers.add("Host", "office.chaoxing.com");
                headers.add("Connection", "keep-alive");
                headers.add("Keep-Alive", "timeout=10");
                // 注意：Python版本没有设置Referer，所以这里也不设置

                // 打印请求头详情
                log.info("=== API请求头详情 ===");
                headers.forEach((key, value) -> log.info("  {}: {}", key, value));

                // 添加Cookie
                if (!cookies.isEmpty()) {
                    log.info("=== Cookie详情 ===");
                    cookies.forEach((key, value) -> log.info("  {}: {}", key, value));

                    String cookieString = cookies.entrySet().stream()
                        .map(entry -> entry.getKey() + "=" + entry.getValue())
                        .reduce((a, b) -> a + "; " + b)
                        .orElse("");
                    headers.add("Cookie", cookieString);
                    log.info("Cookie字符串: {}", cookieString);
                } else {
                    log.warn("没有Cookie可用");
                }

                // 按照Python脚本使用params方式（URL参数）而不是form data
                String paramString = submitParams.entrySet().stream()
                    .map(entry -> {
                        try {
                            return URLEncoder.encode(entry.getKey(), "UTF-8") + "=" + URLEncoder.encode(entry.getValue(), "UTF-8");
                        } catch (Exception e) {
                            return entry.getKey() + "=" + entry.getValue();
                        }
                    })
                    .collect(Collectors.joining("&"));
                String fullUrl = submitUrl + "?" + paramString;

                log.info("=== API请求详情 ===");
                log.info("请求方法: POST");
                log.info("基础URL: {}", submitUrl);
                log.info("请求参数详情: {}", submitParams);
                log.info("参数字符串: {}", paramString);
                log.info("完整请求URL: {}", fullUrl);
                log.info("请求体: 无 (使用URL参数)");

                HttpEntity<String> request = new HttpEntity<>(null, headers);
                ResponseEntity<String> response = restTemplate.exchange(fullUrl, HttpMethod.POST, request, String.class);

                log.info("=== API响应详情 ===");
                log.info("响应状态码: {}", response.getStatusCode());
                log.info("响应头: {}", response.getHeaders());
                log.info("响应体长度: {}", response.getBody() != null ? response.getBody().length() : 0);
                log.info("响应体内容: {}", response.getBody());
                
                // 5. 解析预约结果
                log.debug("预约步骤5: 解析预约结果");
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    log.debug("预约响应内容: {}", responseBody);

                    Map<String, Object> result = JsonUtils.fromJsonToMap(responseBody);

                    if (result != null) {
                        boolean success = Boolean.TRUE.equals(result.get("success"));
                        String message = (String) result.get("msg");

                        log.info("预约提交结果: roomId={}, seatId={}, success={}, message={}",
                            roomId, seatId, success, message);
                        log.debug("完整预约响应: {}", result);

                        return success;
                    } else {
                        log.error("预约响应解析失败: 无法解析JSON响应");
                    }
                } else {
                    log.error("预约请求失败: status={}, body={}",
                        response.getStatusCode(), response.getBody());
                }

                return false;
                
            } catch (Exception e) {
                log.error("提交预约异常: roomId={}, seatId={}, error={}", roomId, seatId, e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 获取登录页面，建立会话
         */
        private void getLoginPage() {
            try {
                HttpHeaders headers = createHeaders();
                HttpEntity<String> request = new HttpEntity<>(headers);
                
                ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/mlogin?loginType=1&newversion=true&fid=", 
                    HttpMethod.GET, request, String.class);
                
                extractCookies(response);
                log.debug("获取登录页面成功");
            } catch (Exception e) {
                log.warn("获取登录页面失败: {}", e.getMessage());
            }
        }
        
        /**
         * 获取页面token
         *
         * @param roomId 房间ID (实际的room_id字符串)
         * @param seatId 座位ID
         * @return token字符串
         */
        private String getPageToken(String roomId, String seatId) {
            try {
                String url = officeUrl + "/front/third/apps/seat/code?id=" + roomId + "&seatNum=" + seatId;
                log.info("获取页面token: url={}", url);

                // 使用与Python版本相同的login_headers，而不是createHeaders()
                HttpHeaders headers = createLoginHeaders();
                // 移除Host头，让RestTemplate自动设置
                headers.remove("Host");
                // 设置正确的Referer
                headers.set("Referer", "https://office.chaoxing.com/");

                log.info("=== Token获取请求头详情 ===");
                headers.forEach((key, value) -> log.info("  {}: {}", key, value));

                HttpEntity<String> request = new HttpEntity<>(headers);

                log.debug("发送token页面请求，当前Cookie数量: {}", cookies.size());
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);

                log.debug("token页面响应: status={}, bodyLength={}",
                    response.getStatusCode(),
                    response.getBody() != null ? response.getBody().length() : 0);

                if (response.getStatusCode().is2xxSuccessful()) {
                    String html = response.getBody();

                    // 调试：输出HTML内容的前1000个字符
                    log.debug("获取到的HTML内容前1000字符: {}",
                        html != null && html.length() > 1000 ? html.substring(0, 1000) : html);

                    // 尝试多种token提取方式
                    String token = extractTokenFromHtml(html);
                    if (token != null) {
                        log.info("页面token提取成功: {}", token);
                        return token;
                    }
                } else {
                    log.error("token页面请求失败: status={}, body={}",
                        response.getStatusCode(), response.getBody());
                }

                log.error("从页面HTML中未找到token");
                return null;
                
            } catch (Exception e) {
                log.error("获取页面token异常: roomId={}, seatId={}, error={}", roomId, seatId, e.getMessage(), e);
                return null;
            }
        }

        /**
         * 从HTML中提取token，尝试多种提取方式
         *
         * @param html HTML内容
         * @return token字符串，如果未找到返回null
         */
        private String extractTokenFromHtml(String html) {
            if (html == null || html.isEmpty()) {
                log.warn("HTML内容为空，无法提取token");
                return null;
            }

            // 方式1: Python版本的正则表达式 token: 'value'
            Pattern pattern1 = Pattern.compile("token: '(.*?)'");
            Matcher matcher1 = pattern1.matcher(html);
            if (matcher1.find()) {
                String token = matcher1.group(1);
                log.debug("使用方式1提取token成功: {}", token);
                return token;
            }

            // 方式2: 双引号版本 token: "value"
            Pattern pattern2 = Pattern.compile("token: \"(.*?)\"");
            Matcher matcher2 = pattern2.matcher(html);
            if (matcher2.find()) {
                String token = matcher2.group(1);
                log.debug("使用方式2提取token成功: {}", token);
                return token;
            }

            // 方式3: 等号版本 token = 'value'
            Pattern pattern3 = Pattern.compile("token\\s*=\\s*'(.*?)'");
            Matcher matcher3 = pattern3.matcher(html);
            if (matcher3.find()) {
                String token = matcher3.group(1);
                log.debug("使用方式3提取token成功: {}", token);
                return token;
            }

            // 方式4: 等号双引号版本 token = "value"
            Pattern pattern4 = Pattern.compile("token\\s*=\\s*\"(.*?)\"");
            Matcher matcher4 = pattern4.matcher(html);
            if (matcher4.find()) {
                String token = matcher4.group(1);
                log.debug("使用方式4提取token成功: {}", token);
                return token;
            }

            // 方式5: 查找所有可能的token相关内容
            Pattern pattern5 = Pattern.compile("(?i)token[\\s:=]+['\"]([^'\"]+)['\"]");
            Matcher matcher5 = pattern5.matcher(html);
            if (matcher5.find()) {
                String token = matcher5.group(1);
                log.debug("使用方式5提取token成功: {}", token);
                return token;
            }

            log.warn("所有token提取方式都失败");

            // 输出HTML中包含token的行，用于调试
            String[] lines = html.split("\n");
            for (int i = 0; i < lines.length; i++) {
                if (lines[i].toLowerCase().contains("token")) {
                    log.debug("HTML第{}行包含token: {}", i + 1, lines[i].trim());
                }
            }

            return null;
        }

        /**
         * 创建HTTP请求头 - 模拟Python脚本的请求头
         *
         * @return HttpHeaders
         */
        private HttpHeaders createHeaders() {
            HttpHeaders headers = new HttpHeaders();

            // 使用与Python脚本完全相同的User-Agent
            headers.add("User-Agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 添加Python脚本中的所有请求头
            headers.add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3");
            headers.add("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");
            headers.add("Accept-Encoding", "gzip, deflate, br");
            headers.add("Connection", "keep-alive");
            headers.add("Pragma", "no-cache");
            headers.add("Cache-Control", "no-cache");

            // 添加Chrome浏览器的安全相关头部
            headers.add("Sec-Ch-Ua", "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"");
            headers.add("Sec-Ch-Ua-Mobile", "?0");
            headers.add("Sec-Ch-Ua-Platform", "\"Linux\"");
            headers.add("Sec-Fetch-Dest", "document");
            headers.add("Sec-Fetch-Mode", "navigate");
            headers.add("Sec-Fetch-Site", "none");
            headers.add("Sec-Fetch-User", "?1");
            headers.add("Upgrade-Insecure-Requests", "1");

            // 添加Cookie
            if (!cookies.isEmpty()) {
                String cookieString = cookies.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .reduce((a, b) -> a + "; " + b)
                    .orElse("");
                headers.add("Cookie", cookieString);
            }

            return headers;
        }

        /**
         * 创建登录专用请求头 - 模拟Python脚本的登录请求头
         *
         * @return HttpHeaders
         */
        private HttpHeaders createLoginHeaders() {
            HttpHeaders headers = new HttpHeaders();

            // 使用Python脚本中login_headers的配置
            headers.add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3");
            headers.add("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7");

            // Python脚本中使用的是iPhone User-Agent
            headers.add("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638");

            headers.add("X-Requested-With", "XMLHttpRequest");
            headers.add("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            // 注意：不设置Host，让RestTemplate自动设置正确的Host

            // 添加Cookie
            if (!cookies.isEmpty()) {
                String cookieString = cookies.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .reduce((a, b) -> a + "; " + b)
                    .orElse("");
                headers.add("Cookie", cookieString);
            }

            return headers;
        }

        /**
         * 从响应中提取Cookie
         *
         * @param response HTTP响应
         */
        private void extractCookies(ResponseEntity<String> response) {
            List<String> setCookieHeaders = response.getHeaders().get("Set-Cookie");
            if (setCookieHeaders != null) {
                for (String cookie : setCookieHeaders) {
                    String[] parts = cookie.split(";")[0].split("=", 2);
                    if (parts.length == 2) {
                        cookies.put(parts[0], parts[1]);
                    }
                }
                log.debug("提取到 {} 个Cookie", cookies.size());
            }
        }
    }

    /**
     * AES加密 (复制Python脚本的加密逻辑)
     *
     * @param plainText 明文
     * @return 加密后的Base64字符串
     */
    private String aesEncrypt(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(AES_IV.getBytes(StandardCharsets.UTF_8));

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);

        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage(), e);
            return plainText;
        }
    }

    /**
     * 计算enc参数 (参数签名) - 完全按照Python脚本的逻辑
     *
     * @param params 参数Map
     * @return 签名字符串
     */
    private String calculateEnc(Map<String, String> params) {
        try {
            // 按照Python脚本的逻辑计算参数签名
            // Python代码：
            // processed_info = resort(submit_info)  # 按key排序
            // needed = [add(add('[', key), '=' + value) + ']' for key, value in processed_info.items()]
            // pattern = "%sd`~7^/>N4!Q#){''"
            // needed.append(add('[', pattern) + ']')
            // seq = ''.join(needed)
            // return md5(seq.encode("utf-8")).hexdigest()

            StringBuilder sb = new StringBuilder();

            // 1. 按key排序并构建 [key=value] 格式
            params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> sb.append("[")
                    .append(entry.getKey())
                    .append("=")
                    .append(entry.getValue())
                    .append("]"));

            // 2. 添加固定模式
            String pattern = "%sd`~7^/>N4!Q#){'";
            sb.append("[").append(pattern).append("]");

            String seq = sb.toString();
            log.debug("enc计算字符串: {}", seq);

            // 3. MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(seq.getBytes(StandardCharsets.UTF_8));

            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (Exception e) {
            log.error("计算enc参数失败: {}", e.getMessage(), e);
            return "";
        }
    }

    /**
     * 从数据库获取房间的实际room_id
     * 支持两种输入：数据库表ID或者room_id字段值
     *
     * @param roomIdOrTableId 数据库表中的房间ID或者room_id字段值
     * @return 实际的room_id字符串
     */
    private String getRoomIdFromDatabase(Long roomIdOrTableId) {
        try {
            // 首先尝试按表ID查询
            Room room = roomService.getRoomById(roomIdOrTableId);
            if (room != null && room.getRoomId() != null) {
                log.debug("通过表ID找到房间: tableId={}, roomId={}", roomIdOrTableId, room.getRoomId());
                return room.getRoomId();
            }

            // 如果按表ID查询失败，尝试按room_id字段查询
            log.debug("按表ID查询失败，尝试按room_id字段查询: {}", roomIdOrTableId);
            room = roomService.getRoomByRoomId(roomIdOrTableId.toString());
            if (room != null && room.getRoomId() != null) {
                log.debug("通过room_id字段找到房间: roomId={}", room.getRoomId());
                return room.getRoomId();
            }

            log.warn("房间不存在: roomIdOrTableId={}", roomIdOrTableId);
            return null;
        } catch (Exception e) {
            log.error("查询房间信息失败: roomIdOrTableId={}, error={}", roomIdOrTableId, e.getMessage(), e);
            return null;
        }
    }
}
