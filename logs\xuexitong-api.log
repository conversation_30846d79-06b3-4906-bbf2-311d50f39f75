2025-06-06 09:56:14.374  INFO --- [pool-1-thread-4] c.s.service.XuexitongApiService : 开始执行学习通预约: reservationId=50, userId=20, roomId=4991, seatId=005
2025-06-06 09:56:14.382  INFO --- [pool-1-thread-4] c.s.service.XuexitongApiService : 预约时间详情: startTime=12:30, endTime=22:00, openTime=08:00:00, type=SAME_DAY
2025-06-06 09:56:14.383 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 步骤1: 获取用户信息, userId=20
2025-06-06 09:56:14.386  INFO --- [pool-1-thread-4] c.s.service.XuexitongApiService : 用户信息获取成功: username=18755869972, name=预约陶承程, remainingDays=2
2025-06-06 09:56:14.387 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 步骤2: 检查用户剩余天数, remainingDays=2
2025-06-06 09:56:14.387 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 步骤3: 创建学习通会话
2025-06-06 09:56:14.388 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 步骤4: 开始登录学习通, username=18755869972
2025-06-06 09:56:14.388  INFO --- [pool-1-thread-4] c.s.service.XuexitongApiService : 开始登录学习通: username=18755869972
2025-06-06 09:56:14.388 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录步骤1: 获取登录页面，建立会话
2025-06-06 09:56:14.517 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 提取到 1 个Cookie
2025-06-06 09:56:14.517 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 获取登录页面成功
2025-06-06 09:56:14.517 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录页面获取完成，当前Cookie数量: 1
2025-06-06 09:56:14.518 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录步骤2: 构建登录参数
2025-06-06 09:56:14.519 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录参数构建完成: fid=-1, refer=office.chaoxing.com
2025-06-06 09:56:14.519 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录步骤3: 发送登录请求到 https://passport2.chaoxing.com/fanyalogin
2025-06-06 09:56:14.573 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录请求响应: status=200 OK, bodyLength=93
2025-06-06 09:56:14.574 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录步骤4: 解析登录结果
2025-06-06 09:56:14.574 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 登录响应内容: {"url":"http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode","status":true}
2025-06-06 09:56:14.574 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 提取到 11 个Cookie
2025-06-06 09:56:14.575  INFO --- [pool-1-thread-4] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972, 总Cookie数量=11
2025-06-06 09:56:14.576  INFO --- [pool-1-thread-4] c.s.service.XuexitongApiService : 学习通登录成功: username=18755869972
2025-06-06 09:56:14.576 DEBUG --- [pool-1-thread-4] c.s.service.XuexitongApiService : 步骤5: 获取实际的room_id
2025-06-06 09:56:14.583  WARN --- [pool-1-thread-4] c.s.service.XuexitongApiService : 房间不存在或room_id为空: roomTableId=4991
2025-06-06 09:56:14.583 ERROR --- [pool-1-thread-4] c.s.service.XuexitongApiService : 无法获取房间的实际room_id: roomTableId=4991
