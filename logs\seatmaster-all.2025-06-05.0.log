2025-06-05 15:48:37.323  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 15:48:37.337  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 13332 (C:\Users\<USER>\Desktop\seatMaster\backend\target\classes started by skr tao in C:\Users\<USER>\Desktop\seatMaster)
2025-06-05 15:48:37.338 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 15:48:37.339  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 15:48:39.075  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port(s): 8081 (http)
2025-06-05 15:48:39.077  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 15:48:39.077  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 15:48:39.077  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 15:48:39.095  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 15:48:39.104  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 15:48:39.107  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 15:48:39.107  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 15:48:39.242  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 15:48:39.242  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1823 ms
2025-06-05 15:48:39.320 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 15:48:40.267  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a985606e-f421-483e-89a5-a0cd760caf05

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 15:48:40.389  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2a99ca99, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@39f0c343, org.springframework.security.web.context.SecurityContextPersistenceFilter@54495935, org.springframework.security.web.header.HeaderWriterFilter@70be89ec, org.springframework.web.filter.CorsFilter@781654f8, org.springframework.security.web.authentication.logout.LogoutFilter@2f09e6b2, com.seatmaster.filter.JwtAuthenticationFilter@28369db0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3a6e9856, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@482f7af0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2abc55c4, org.springframework.security.web.session.SessionManagementFilter@ca7e37f, org.springframework.security.web.access.ExceptionTranslationFilter@79195c22, org.springframework.security.web.access.intercept.AuthorizationFilter@5175d9ad]
2025-06-05 15:48:40.832  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 15:48:40.866  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 15:48:40.885  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 4.636 seconds (JVM running for 6.067)
2025-06-05 15:49:00.092  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 15:49:00.273  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 15:50:24.871  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-05 15:50:24.881  INFO --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-06-05 21:15:44.629  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 21:15:44.640  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 149016 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster)
2025-06-05 21:15:44.645 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 21:15:44.645  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 21:15:46.746  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 21:15:46.750  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 21:15:46.751  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 21:15:46.751  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 21:15:46.774  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 21:15:46.787  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 21:15:46.792  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 21:15:46.792  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 21:15:46.951  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 21:15:46.951  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2213 ms
2025-06-05 21:15:47.048 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 21:15:48.401  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: d99bf9d0-773f-40e7-9efc-0b7943867c7b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 21:15:48.600  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@538cd0f2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@238ad8c, org.springframework.security.web.context.SecurityContextPersistenceFilter@9ec531, org.springframework.security.web.header.HeaderWriterFilter@2577d6c8, org.springframework.web.filter.CorsFilter@430fa4ef, org.springframework.security.web.authentication.logout.LogoutFilter@47dd778, com.seatmaster.filter.JwtAuthenticationFilter@45efc20d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@210f0cc1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f95cd51, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1761de10, org.springframework.security.web.session.SessionManagementFilter@5e8a459, org.springframework.security.web.access.ExceptionTranslationFilter@30cdae70, org.springframework.security.web.access.intercept.AuthorizationFilter@3ae66c85]
2025-06-05 21:15:49.363  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 21:15:49.410  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 21:15:49.429  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 5.723 seconds (JVM running for 6.486)
2025-06-05 21:16:00.118  INFO --- [   scheduling-2] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 21:16:00.445  INFO --- [   scheduling-2] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 21:16:21.870  INFO --- [nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 21:16:21.877  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-05 21:16:21.905  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 17 ms
2025-06-05 21:16:22.707  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 21:16:22.718  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 21:16:22.718  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 21:16:22.758  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 21:16:22.758  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 21:16:22.765  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 21:16:22.788  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 21:16:22.790  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 21:16:22.790  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=AUTO_FAILED, 开始时间=15:30, 结束时间=20:30
2025-06-05 21:16:22.797  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
2025-06-05 21:16:43.804  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 21:16:43.813  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 21:16:43.813  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 21:16:43.822  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 21:16:43.822  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 21:16:43.826  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 21:16:43.836  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 21:16:43.836  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 21:16:43.836  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=AUTO_PENDING, 开始时间=15:30, 结束时间=20:30
2025-06-05 21:16:43.842  INFO --- [nio-8081-exec-1] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
2025-06-05 21:50:44.092  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 21:50:44.109  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 143924 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster)
2025-06-05 21:50:44.111 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 21:50:44.112  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 21:50:47.202  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 21:50:47.204  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 21:50:47.206  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 21:50:47.207  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 21:50:47.238  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 21:50:47.256  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 21:50:47.267  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 21:50:47.268  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 21:50:47.452  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 21:50:47.453  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3191 ms
2025-06-05 21:50:47.604 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 21:50:49.522  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 41435211-7370-4a86-8c7e-e88148850e21

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 21:50:49.859  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@c827db, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@377c68c6, org.springframework.security.web.context.SecurityContextPersistenceFilter@43d455c9, org.springframework.security.web.header.HeaderWriterFilter@30cdae70, org.springframework.web.filter.CorsFilter@538cd0f2, org.springframework.security.web.authentication.logout.LogoutFilter@d4602a, com.seatmaster.filter.JwtAuthenticationFilter@75459c75, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9ec531, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@210f0cc1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@238ad8c, org.springframework.security.web.session.SessionManagementFilter@44f9779c, org.springframework.security.web.access.ExceptionTranslationFilter@bf71cec, org.springframework.security.web.access.intercept.AuthorizationFilter@11a82d0f]
2025-06-05 21:50:50.763  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 21:50:50.844  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 21:50:50.874  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 8.091 seconds (JVM running for 9.068)
2025-06-05 21:51:00.159  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 21:51:00.464  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 21:51:39.546  INFO --- [nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 21:51:39.547  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-05 21:51:39.550  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-05 21:51:40.227  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 21:51:40.237  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 21:51:40.238  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 21:51:40.280  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 21:51:40.280  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 21:51:40.291  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 21:51:40.309  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 21:51:40.311  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 21:51:40.311  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=AUTO_PENDING, 开始时间=15:30, 结束时间=20:30
2025-06-05 21:51:40.319  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
2025-06-05 22:10:01.461  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 22:10:01.485  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 1572 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster)
2025-06-05 22:10:01.489 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 22:10:01.489  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 22:10:04.598  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 22:10:04.601  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 22:10:04.602  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 22:10:04.602  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 22:10:04.629  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 22:10:04.653  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 22:10:04.668  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 22:10:04.668  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 22:10:04.843  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 22:10:04.844  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3229 ms
2025-06-05 22:10:04.972 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 22:10:06.736  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e3fa04b2-b90f-4f3b-9bdf-e330dae4882e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 22:10:07.087  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@680362a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3569edd5, org.springframework.security.web.context.SecurityContextPersistenceFilter@6c000e0c, org.springframework.security.web.header.HeaderWriterFilter@2c30b71f, org.springframework.web.filter.CorsFilter@1f651cd8, org.springframework.security.web.authentication.logout.LogoutFilter@2fa3be26, com.seatmaster.filter.JwtAuthenticationFilter@63b1d4fa, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@44f9779c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e8a459, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7d0332e1, org.springframework.security.web.session.SessionManagementFilter@1654a892, org.springframework.security.web.access.ExceptionTranslationFilter@194152cf, org.springframework.security.web.access.intercept.AuthorizationFilter@47dd778]
2025-06-05 22:10:07.995  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 22:10:08.075  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8081 (http) with context path ''
2025-06-05 22:10:08.108  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 7.972 seconds (JVM running for 8.897)
2025-06-05 22:11:00.192  INFO --- [   scheduling-2] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 22:11:00.797  INFO --- [   scheduling-2] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 22:12:39.858  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 22:12:39.874  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 175672 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster)
2025-06-05 22:12:39.876 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 22:12:39.876  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 22:12:42.356  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8081 (http)
2025-06-05 22:12:42.361  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 22:12:42.362  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 22:12:42.364  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 22:12:42.378  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 22:12:42.399  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8081"]
2025-06-05 22:12:42.404  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 22:12:42.405  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 22:12:42.557  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 22:12:42.559  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2567 ms
2025-06-05 22:12:42.672 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 22:12:44.266  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: ae788b8d-ce42-4a5e-a90b-f87f8ddcd1e7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 22:12:44.545  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@538cd0f2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@238ad8c, org.springframework.security.web.context.SecurityContextPersistenceFilter@9ec531, org.springframework.security.web.header.HeaderWriterFilter@2577d6c8, org.springframework.web.filter.CorsFilter@430fa4ef, org.springframework.security.web.authentication.logout.LogoutFilter@47dd778, com.seatmaster.filter.JwtAuthenticationFilter@3e5499cc, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@210f0cc1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f95cd51, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1761de10, org.springframework.security.web.session.SessionManagementFilter@5e8a459, org.springframework.security.web.access.ExceptionTranslationFilter@30cdae70, org.springframework.security.web.access.intercept.AuthorizationFilter@3ae66c85]
2025-06-05 22:12:45.194  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8081"]
2025-06-05 22:12:45.215  WARN --- [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8081 is already in use
2025-06-05 22:12:45.227  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Pausing ProtocolHandler ["http-nio-8081"]
2025-06-05 22:12:45.229  INFO --- [           main] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-06-05 22:12:45.242  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Stopping ProtocolHandler ["http-nio-8081"]
2025-06-05 22:12:45.242  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Destroying ProtocolHandler ["http-nio-8081"]
2025-06-05 22:12:45.259  INFO --- [           main] ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-05 22:12:45.295 ERROR --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-06-05 22:13:30.109  INFO --- [kground-preinit] o.h.validator.internal.util.Version      : HV000001: Hibernate Validator 6.2.5.Final
2025-06-05 22:13:30.127  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Starting SeatReservationApplication v1.0.0 using Java 17.0.12 on LAPTOP-S5NOOCEM with PID 142232 (C:\Users\<USER>\Desktop\seatMaster\backend\target\seat-reservation-backend-1.0.0.jar started by skr tao in C:\Users\<USER>\Desktop\seatMaster)
2025-06-05 22:13:30.130 DEBUG --- [           main] c.seatmaster.SeatReservationApplication  : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-05 22:13:30.131  INFO --- [           main] c.seatmaster.SeatReservationApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-05 22:13:33.455  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8082 (http)
2025-06-05 22:13:33.457  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : Loaded Apache Tomcat Native library [1.3.0] using APR version [1.7.4].
2025-06-05 22:13:33.458  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true].
2025-06-05 22:13:33.458  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-05 22:13:33.469  INFO --- [           main] o.a.catalina.core.AprLifecycleListener   : OpenSSL successfully initialized [OpenSSL 3.0.13 30 Jan 2024]
2025-06-05 22:13:33.479  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Initializing ProtocolHandler ["http-nio-8082"]
2025-06-05 22:13:33.484  INFO --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-05 22:13:33.485  INFO --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-05 22:13:33.630  INFO --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-05 22:13:33.630  INFO --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3354 ms
2025-06-05 22:13:33.750 DEBUG --- [           main] c.s.filter.JwtAuthenticationFilter       : Filter 'jwtAuthenticationFilter' configured for use
2025-06-05 22:13:35.876  WARN --- [           main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 9408a58d-4677-4547-81bb-c7631bd5db4e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-05 22:13:36.266  INFO --- [           main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@16943e88, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4604b900, org.springframework.security.web.context.SecurityContextPersistenceFilter@503fbbc6, org.springframework.security.web.header.HeaderWriterFilter@2aa27288, org.springframework.web.filter.CorsFilter@73d6d0c, org.springframework.security.web.authentication.logout.LogoutFilter@3163987e, com.seatmaster.filter.JwtAuthenticationFilter@42e3ede4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@109f5dd8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4a325eb9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e36bb2a, org.springframework.security.web.session.SessionManagementFilter@49298ce7, org.springframework.security.web.access.ExceptionTranslationFilter@2d140a7, org.springframework.security.web.access.intercept.AuthorizationFilter@4c9e9fb8]
2025-06-05 22:13:37.690  INFO --- [           main] o.a.coyote.http11.Http11NioProtocol      : Starting ProtocolHandler ["http-nio-8082"]
2025-06-05 22:13:37.838  INFO --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8082 (http) with context path ''
2025-06-05 22:13:37.895  INFO --- [           main] c.seatmaster.SeatReservationApplication  : Started SeatReservationApplication in 9.035 seconds (JVM running for 9.782)
2025-06-05 22:14:00.146  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-05 22:14:00.492  INFO --- [   scheduling-1] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-06-05 22:14:08.657  INFO --- [nio-8081-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 22:14:08.658  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-05 22:14:08.660  INFO --- [nio-8081-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-05 22:14:09.249  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 22:14:09.258  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 22:14:09.258  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 22:14:09.293  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 22:14:09.295  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 22:14:09.300  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 22:14:09.316  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 22:14:09.317  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 22:14:09.318  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=005, 状态=AUTO_PENDING, 开始时间=15:30, 结束时间=20:30
2025-06-05 22:14:09.326  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
2025-06-05 22:21:20.209  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 22:21:20.213  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 22:21:20.213  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 22:21:20.217  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 22:21:20.218  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 22:21:20.220  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 22:21:20.226  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 22:21:20.226  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 22:21:20.226  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=005, 状态=AUTO_PENDING, 开始时间=15:30, 结束时间=20:30
2025-06-05 22:21:20.230  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
2025-06-05 22:23:15.204  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 获取用户信息请求，用户名: 18755869972
2025-06-05 22:23:15.209  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到用户: 18755869972, ID: 20, 剩余天数: 3
2025-06-05 22:23:15.209  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 开始检查用户20的剩余天数，当前剩余天数: 3
2025-06-05 22:23:15.213  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 完成检查用户20的预约删除操作
2025-06-05 22:23:15.213  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 正在查询用户ID 20的预约信息
2025-06-05 22:23:15.215  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的预约总数: 2
2025-06-05 22:23:15.217  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 用户ID 20的所有预约记录数: 2
2025-06-05 22:23:15.218  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=49, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001, 状态=ACTIVE, 开始时间=20:30, 结束时间=21:30
2025-06-05 22:23:15.218  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 预约记录: ID=46, 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=005, 状态=AUTO_PENDING, 开始时间=15:30, 结束时间=20:30
2025-06-05 22:23:15.222  INFO --- [nio-8081-exec-3] c.seatmaster.controller.UserController   : 找到最新预约: 学校=吉林农业大学, 房间=A区一楼-A104-自主学习室, 座位=001
